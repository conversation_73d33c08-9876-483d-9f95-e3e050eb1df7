# Building the MSI Installer

This directory contains two build scripts to help you create the MSI installer:

1. `build.ps1` - PowerShell script (recommended)
2. `build.bat` - Batch script (alternative)

## Prerequisites

Before running either build script, make sure you have:

1. **WiX Toolset v3.x** installed (v3.11 recommended)
   - Download from: https://wixtoolset.org/releases/
   - Default installation path: `C:\Program Files (x86)\WiX Toolset v3.11`

2. **Visual Studio** with C++ development tools installed
   - Visual Studio 2019 or 2022 recommended
   - Make sure to install the "Desktop development with C++" workload

## Using the PowerShell Script (Recommended)

1. Open PowerShell as Administrator
2. Navigate to the msi-package directory
3. Run the script:
   ```
   .\build.ps1
   ```

The script will:
- Create placeholder banner.bmp and dialog.bmp files if they don't exist
- Build the FileBrowse.dll custom action
- Compile the WiX source files
- Create the MSI in the output directory

## Using the Batch Script (Alternative)

1. Open Command Prompt as Administrator
2. Navigate to the msi-package directory
3. Run the script:
   ```
   build.bat
   ```

## Customizing the Build

To customize the build, you can modify the following variables at the top of either script:

### In build.ps1:
```powershell
$productName = "MyApplication"
$outputDir = ".\output"
$wixBinPath = "C:\Program Files (x86)\WiX Toolset v3.11\bin"
$vsDevCmd = "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
```

### In build.bat:
```batch
set PRODUCT_NAME=MyApplication
set OUTPUT_DIR=output
set WIX_BIN_PATH=C:\Program Files (x86)\WiX Toolset v3.11\bin
set VS_DEV_CMD=C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat
```

## Troubleshooting

If you encounter errors during the build process:

1. **WiX Toolset not found**
   - Make sure WiX Toolset v3.x is installed
   - Update the path in the build script to match your installation

2. **Visual Studio not found**
   - Make sure Visual Studio with C++ development tools is installed
   - Update the path in the build script to match your installation

3. **Custom action DLL build fails**
   - Make sure the WiX SDK is installed with the WiX Toolset
   - Check that the paths to the WiX SDK include and library directories are correct

4. **WiX compilation fails**
   - Check the error messages for syntax errors in the .wxs files
   - Make sure all referenced files exist (banner.bmp, dialog.bmp, etc.)

5. **MSI creation fails**
   - Check that all required WiX extensions are referenced
   - Make sure all object files were successfully created
