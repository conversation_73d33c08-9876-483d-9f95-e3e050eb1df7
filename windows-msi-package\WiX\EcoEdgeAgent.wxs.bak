<?xml Version="1.0.0.0" encoding="utf-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs">
  <Product Id="*" Name="Eco Edge Agent" Language="1033" Version="1.0.0.0" Manufacturer="YourCompany" UpgradeCode="D2B5C5A2-7F7B-4E3B-8D0A-1F8B6B2D2C1E">
    <!-- Property to hold the optional config file path -->
    <Property Id="CONFIGFILEPATH" Value="" />
    <!-- Define WIXUI_INSTALLDIR property for WixUI_InstallDir -->
    <Property Id="WIXUI_INSTALLDIR" Value="INSTALLFOLDER" />
    <Package InstallerVersion="1.0.0.0" Compressed="yes" InstallScope="perMachine" />
    <MediaTemplate />
    
    <!-- Define the directory structure -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="EcoEdgeAgent" />
      </Directory>
    </Directory>

    <!-- UI for optional config file selection -->
    <UI>
      <UIRef Id="WixUI_InstallDir" />
      <!-- Override InstallDirDlg to make the install directory field read-only and remove the Browse button -->
      <Dialog Id="InstallDirDlg" Width="370" Height="270" Title="[ProductName] Setup">
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text>{\WixUI_Font_Title}Choose Install Location</Text>
        </Control>
        <Control Id="FolderLabel" Type="Text" X="20" Y="73" Width="290" Height="15">
          <Text>Destination Folder:</Text>
        </Control>
        <Control Id="Folder" Type="PathEdit" X="20" Y="90" Width="320" Height="18" Property="INSTALLFOLDER" Indirect="yes" ReadOnly="yes"/>
        <!-- Browse button removed -->
        <!-- ... rest of the default controls (Next, Back, Cancel) ... -->
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="Next">
          <Publish Event="EndDialog" Value="Return">1</Publish>
        </Control>
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="Back">
          <Publish Event="NewDialog" Value="WelcomeDlg">1</Publish>
        </Control>
        <Control Id="Cancel" Type="PushButton" X="292" Y="243" Width="56" Height="17" Cancel="yes" Text="Cancel">
          <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
        </Control>
      </Dialog>
    </UI>

    <!-- Custom action to set CONFIGFILEPATH from BROWSE_PATH -->
    <CustomAction Id="SetConfigFilePath" Property="CONFIGFILEPATH" Value="[BROWSE_PATH]" />
    <Feature Id="ProductFeature" Title="Eco Edge Agent" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentRef Id="InstallScript" />
    </Feature>

    <!-- Install install.ps1 to the application folder -->
    <DirectoryRef Id="INSTALLFOLDER">
      <Component Id="InstallScript" Guid="A1B2C3D4-E5F6-4A7B-8C9D-1234567890AB">
        <File Id="InstallPs1" Source="..\scripts\install.ps1" KeyPath="yes" />
      </Component>
    </DirectoryRef>

    <!-- Custom action to run install.ps1 after files are installed -->
    <CustomAction Id="RunInstallScript"
                  FileKey="InstallPs1"
                  ExeCommand="powershell.exe -ExecutionPolicy Bypass -File &quot;[INSTALLFOLDER]install.ps1&quot; -ConfigFilePath &quot;[CONFIGFILEPATH]&quot;"
                  Execute="deferred"
                  Return="check"
                  Impersonate="no" />

    <InstallExecuteSequence>
      <Custom Action="RunInstallScript" After="InstallFiles">NOT Installed</Custom>
    </InstallExecuteSequence>

    <!-- TODO: Add UI and custom actions for config file selection and install logic -->
  </Product>
</Wix>
