Windows Installer XML Toolset Toolset Harvester version 3.11.2.4516
Copyright (c) .NET Foundation and contributors. All rights reserved.

Windows Installer XML Toolset Compiler version 3.11.2.4516
Copyright (c) .NET Foundation and contributors. All rights reserved.

FlutterAssets.wxs
Windows Installer XML Toolset Compiler version 3.11.2.4516
Copyright (c) .NET Foundation and contributors. All rights reserved.

Product.wxs
WixUI_Custom.wxs
FileBrowseDlg.wxs
InstallDirDlgCustom.wxs
Windows Installer XML Toolset Linker version 3.11.2.4516
Copyright (c) .NET Foundation and contributors. All rights reserved.

C:\agent\_work\66\s\src\ext\UtilExtension\wixlib\UtilExtension_Platform.wxi(203) : error LGHT0091 : Duplicate symbol 'Binary:WixCA' found. This typically means that an Id is duplicated. Check to make sure all your identifiers of a given type (File, Component, Feature) are unique.
C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\Product.wxs(198) : error LGHT0092 : Location of symbol related to previous error.
C:\agent\_work\66\s\src\ext\UtilExtension\wixlib\UtilExtension_Platform.wxi(203) : error LGHT0091 : Duplicate symbol 'Binary:WixCA' found. This typically means that an Id is duplicated. Check to make sure all your identifiers of a given type (File, Component, Feature) are unique.
C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\Product.wxs(198) : error LGHT0092 : Location of symbol related to previous error.
