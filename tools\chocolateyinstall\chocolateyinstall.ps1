# Import Chocolatey functions
$toolsDir = "$(Split-Path -parent $MyInvocation.MyCommand.Definition)"
. $toolsDir\helpers.ps1

# Default port values
$frontendPort = "8080"
$backendPort = "9080"

# Default environment variables
$backendEnvVarsMap = @{
    "REST_PORT"                         = "9080"
    "REST_HOST"                         = "0.0.0.0"
    "CLOUD_DEVICE_CERTIFICATE_PATH"     = "/app/certs/device_cert.pem"
    "CLOUD_DEVICE_PRIVATE_KEY_PATH"     = "/app/certs/device_key.pem"
    "CLOUD_CONNECTION_CERT_PATH"        = "/app/certs/connectionCertificate.pem"
    "CLOUD_CONNECTION_PRIVATE_KEY_PATH" = "/app/certs/privateKey.pem"
    "CLOUD_CONNECTION_STRING_PATH"      = "/app/certs/connectionString.txt"
    "CLOUD_BOOTSTRAP_SERVER_URL"        = "https://connectivity.preview.struxurewarecloud.com"  # TODO: Change this value from CICD
    "DB_PATH"                           = "/app/db"
    "AUTH_DB_PATH"                      = "/app/db/auth/auth.db"
    "AUTH_CONFIG_DB_PATH"               = "/app/db/auth/config.db"
    "SNMP_CONFIG_DB_PATH"               = "/app/db/snmp/config.db"
    "CLOUD_CONFIG_DB_PATH"              = "/app/db/cloud/config.db"
}

$frontendEnvVarsMap = @{
    "API_URL"    = "http://octopus-service:9080"
    "NGINX_PORT" = "80"
}

# Default volume mounts (only for backend)
$backendVolumesMap = @{
    # Format: "host_path" = "container_path"
    # Example: "C:\data\backend" = "/app/data"
}

# Parse parameters from Chocolatey
$pp = Get-PackageParameters
if ($pp) {
    # Port parameters
    if ($pp.ContainsKey('frontendPort')) { $frontendPort = $pp.frontendPort }
    if ($pp.ContainsKey('backendPort')) { $backendPort = $pp.backendPort }

    # JSON-based environment variables
    # Format: backendEnvJson='{"VAR1":"value1","VAR2":"value2"}'
    if ($pp.ContainsKey('backendEnvJson')) {
        try {
            $jsonEnvVars = ConvertFrom-Json $pp.backendEnvJson -AsHashtable -ErrorAction Stop
            foreach ($key in $jsonEnvVars.Keys) {
                $backendEnvVarsMap[$key] = $jsonEnvVars[$key]
                Write-Host "Setting backend environment variable from JSON: $key=$($jsonEnvVars[$key])"
            }
        }
        catch {
            Write-Warning "Error parsing backendEnvJson: $_"
            Write-Warning "Using default backend environment variables instead."
        }
    }

    if ($pp.ContainsKey('frontendEnvJson')) {
        try {
            $jsonEnvVars = ConvertFrom-Json $pp.frontendEnvJson -AsHashtable -ErrorAction Stop
            foreach ($key in $jsonEnvVars.Keys) {
                $frontendEnvVarsMap[$key] = $jsonEnvVars[$key]
                Write-Host "Setting frontend environment variable from JSON: $key=$($jsonEnvVars[$key])"
            }
        }
        catch {
            Write-Warning "Error parsing frontendEnvJson: $_"
            Write-Warning "Using default frontend environment variables instead."
        }
    }

    # JSON-based volume mounts
    # Format: backendVolJson='{"host_path1":"/container/path1","host_path2":"/container/path2"}'
    if ($pp.ContainsKey('backendVolJson')) {
        try {
            $jsonVolumes = ConvertFrom-Json $pp.backendVolJson -ErrorAction Stop

            # Process each volume definition
            foreach ($prop in $jsonVolumes.PSObject.Properties) {
                $hostPath = $prop.Name

                # Check if the value is a complex object with containerPath and userInfo
                if ($prop.Value -is [PSCustomObject] -and
                    $prop.Value.PSObject.Properties.Name -contains "containerPath") {
                    $containerPath = $prop.Value.containerPath.ToString()
                    $userInfo = $null

                    if ($prop.Value.PSObject.Properties.Name -contains "userInfo") {
                        $userInfo = $prop.Value.userInfo.ToString()
                    }
                }
                else {
                    # Simple string value
                    $containerPath = $prop.Value.ToString()
                    $userInfo = $null
                }

                # Ensure host path exists
                if (-not (Test-Path $hostPath)) {
                    try {
                        New-Item -ItemType Directory -Path $hostPath -Force | Out-Null
                        Write-Host "Created host directory for volume mount: $hostPath"
                    }
                    catch {
                        Write-Warning "Failed to create host directory for volume mount: $hostPath"
                        Write-Warning "Error: $_"
                        continue
                    }
                }

                # Convert to absolute path
                $hostPath = (Resolve-Path $hostPath).Path

                # Add to volumes map with user info if present
                $backendVolumesMap[$hostPath] = @{
                    "ContainerPath" = $containerPath
                    "UserInfo"      = $userInfo
                }

                if ($userInfo) {
                    Write-Host "Setting backend volume mount from JSON: $hostPath -> $containerPath with user $userInfo"
                }
                else {
                    Write-Host "Setting backend volume mount from JSON: $hostPath -> $containerPath"
                }
            }
        }
        catch {
            Write-Warning "Error parsing backendVolJson: $_"
            Write-Warning "Using default backend volume mounts instead."
        }
    }

    # Frontend volumes are not supported

    # File-based configuration functions
    # Function to parse .env files
    function Parse-EnvFile {
        param (
            [string]$FilePath
        )

        $envVars = @{}

        if (Test-Path $FilePath) {
            $fileContent = Get-Content -Path $FilePath

            foreach ($line in $fileContent) {
                # Skip comments and empty lines
                if ($line.Trim() -eq "" -or $line.Trim().StartsWith("#")) {
                    continue
                }

                # Parse KEY=VALUE format
                if ($line -match "^\s*([^=]+)=(.*)$") {
                    $key = $matches[1].Trim()
                    $value = $matches[2].Trim()

                    # Remove quotes if present
                    if ($value.StartsWith('"') -and $value.EndsWith('"')) {
                        $value = $value.Substring(1, $value.Length - 2)
                    }
                    elseif ($value.StartsWith("'") -and $value.EndsWith("'")) {
                        $value = $value.Substring(1, $value.Length - 2)
                    }

                    $envVars[$key] = $value
                }
            }
        }

        return $envVars
    }

    # Function to process unified configuration file
    function Process-ConfigFile {
        param (
            [string]$FilePath
        )

        $backendEnv = @{}
        $frontendEnv = @{}
        $backendVol = @{}

        if (Test-Path $FilePath) {
            $fileExtension = [System.IO.Path]::GetExtension($FilePath).ToLower()

            if ($fileExtension -eq ".json") {
                # Process JSON file
                try {
                    $fileContent = Get-Content -Path $FilePath -Raw
                    $config = ConvertFrom-Json $fileContent -ErrorAction Stop

                    # Process backend environment variables
                    if ($config.PSObject.Properties.Name -contains "backendEnv") {
                        $backendEnvObj = $config.backendEnv
                        foreach ($prop in $backendEnvObj.PSObject.Properties) {
                            $backendEnv[$prop.Name] = $prop.Value.ToString()
                            Write-Host "Setting backend environment variable from config file: $($prop.Name)=$($prop.Value)"
                        }
                    }

                    # Process frontend environment variables
                    if ($config.PSObject.Properties.Name -contains "frontendEnv") {
                        $frontendEnvObj = $config.frontendEnv
                        foreach ($prop in $frontendEnvObj.PSObject.Properties) {
                            $frontendEnv[$prop.Name] = $prop.Value.ToString()
                            Write-Host "Setting frontend environment variable from config file: $($prop.Name)=$($prop.Value)"
                        }
                    }

                    # Process backend volumes
                    if ($config.PSObject.Properties.Name -contains "backendVol") {
                        $backendVolObj = $config.backendVol
                        foreach ($prop in $backendVolObj.PSObject.Properties) {
                            $containerPath = $prop.Name

                            $hostPath = $prop.Value.ToString()

                            # Ensure host path exists
                            if (-not (Test-Path $hostPath)) {
                                try {
                                    New-Item -ItemType Directory -Path $hostPath -Force | Out-Null
                                    Write-Host "Created host directory for volume mount: $hostPath"
                                }
                                catch {
                                    Write-Warning "Failed to create host directory for volume mount: $hostPath"
                                    Write-Warning "Error: $_"
                                    continue
                                }
                            }

                            # Convert to absolute path
                            $hostPath = (Resolve-Path $hostPath).Path

                            # Store the container path
                            $backendVol[$hostPath] = $containerPath

                            Write-Host "Setting backend volume mount from config file: $hostPath -> $containerPath"
                        }
                    }
                }
                catch {
                    Write-Warning "Error processing config file: $_"
                }
            }
            elseif ($fileExtension -eq ".env" -or $fileExtension -eq "") {
                # Process .env file
                $envVars = Parse-EnvFile -FilePath $FilePath

                foreach ($key in $envVars.Keys) {
                    if ($key.StartsWith("BACKEND_ENV_")) {
                        # Backend environment variable
                        $envName = $key.Substring("BACKEND_ENV_".Length)
                        $backendEnv[$envName] = $envVars[$key]
                        Write-Host "Setting backend environment variable from config file: $envName=$($envVars[$key])"
                    }
                    elseif ($key.StartsWith("FRONTEND_ENV_")) {
                        # Frontend environment variable
                        $envName = $key.Substring("FRONTEND_ENV_".Length)
                        $frontendEnv[$envName] = $envVars[$key]
                        Write-Host "Setting frontend environment variable from config file: $envName=$($envVars[$key])"
                    }
                    elseif ($key.StartsWith("BACKEND_VOL_")) {
                        # Backend volume - in unified config, we use BACKEND_VOL_CONTAINER_PATH=HOST_PATH
                        $containerPath = $key.Substring("BACKEND_VOL_".Length)
                        $hostPath = $envVars[$key]

                        # Ensure host path exists
                        if (-not (Test-Path $hostPath)) {
                            try {
                                New-Item -ItemType Directory -Path $hostPath -Force | Out-Null
                                Write-Host "Created host directory for volume mount: $hostPath"
                            }
                            catch {
                                Write-Warning "Failed to create host directory for volume mount: $hostPath"
                                Write-Warning "Error: $_"
                                continue
                            }
                        }

                        # Convert to absolute path
                        $hostPath = (Resolve-Path $hostPath).Path

                        # Store the container path
                        $backendVol[$hostPath] = $containerPath
                        Write-Host "Setting backend volume mount from config file: $hostPath -> $containerPath"
                    }
                }
            }
            else {
                Write-Warning "Unsupported file format: $fileExtension. Supported formats are .json and .env"
            }
        }
        else {
            Write-Warning "Configuration file not found: $FilePath"
        }

        return @{
            BackendEnv  = $backendEnv
            FrontendEnv = $frontendEnv
            BackendVol  = $backendVol
        }
    }

    # Format: backendEnvFile='C:\path\to\env.json' or 'C:\path\to\.env'
    if ($pp.ContainsKey('backendEnvFile')) {
        try {
            $envFilePath = $pp.backendEnvFile
            if (Test-Path $envFilePath) {
                $fileExtension = [System.IO.Path]::GetExtension($envFilePath).ToLower()

                if ($fileExtension -eq ".json") {
                    # Process JSON file
                    $fileContent = Get-Content -Path $envFilePath -Raw
                    $fileEnvVars = ConvertFrom-Json $fileContent -AsHashtable -ErrorAction Stop
                }
                elseif ($fileExtension -eq ".env" -or $fileExtension -eq "") {
                    # Process .env file
                    $fileEnvVars = Parse-EnvFile -FilePath $envFilePath
                }
                else {
                    Write-Warning "Unsupported file format: $fileExtension. Supported formats are .json and .env"
                    $fileEnvVars = @{}
                }

                foreach ($key in $fileEnvVars.Keys) {
                    $backendEnvVarsMap[$key] = $fileEnvVars[$key]
                    Write-Host "Setting backend environment variable from file: $key=$($fileEnvVars[$key])"
                }
            }
            else {
                Write-Warning "Environment file not found: $envFilePath"
            }
        }
        catch {
            Write-Warning "Error processing backendEnvFile: $_"
            Write-Warning "Using default backend environment variables instead."
        }
    }

    if ($pp.ContainsKey('frontendEnvFile')) {
        try {
            $envFilePath = $pp.frontendEnvFile
            if (Test-Path $envFilePath) {
                $fileExtension = [System.IO.Path]::GetExtension($envFilePath).ToLower()

                if ($fileExtension -eq ".json") {
                    # Process JSON file
                    $fileContent = Get-Content -Path $envFilePath -Raw
                    $fileEnvVars = ConvertFrom-Json $fileContent -AsHashtable -ErrorAction Stop
                }
                elseif ($fileExtension -eq ".env" -or $fileExtension -eq "") {
                    # Process .env file
                    $fileEnvVars = Parse-EnvFile -FilePath $envFilePath
                }
                else {
                    Write-Warning "Unsupported file format: $fileExtension. Supported formats are .json and .env"
                    $fileEnvVars = @{}
                }

                foreach ($key in $fileEnvVars.Keys) {
                    $frontendEnvVarsMap[$key] = $fileEnvVars[$key]
                    Write-Host "Setting frontend environment variable from file: $key=$($fileEnvVars[$key])"
                }
            }
            else {
                Write-Warning "Environment file not found: $envFilePath"
            }
        }
        catch {
            Write-Warning "Error processing frontendEnvFile: $_"
            Write-Warning "Using default frontend environment variables instead."
        }
    }

    # File-based volume mounts
    # Format: backendVolFile='C:\path\to\volumes.json'
    if ($pp.ContainsKey('backendVolFile')) {
        try {
            $volFilePath = $pp.backendVolFile
            if (Test-Path $volFilePath) {
                $fileExtension = [System.IO.Path]::GetExtension($volFilePath).ToLower()

                if ($fileExtension -eq ".json") {
                    # Process JSON file
                    $fileContent = Get-Content -Path $volFilePath -Raw
                    $fileVolumes = ConvertFrom-Json $fileContent -ErrorAction Stop

                    # Process each volume definition
                    foreach ($prop in $fileVolumes.PSObject.Properties) {
                        $containerPath = $prop.Name
                        
                        $hostPath = $prop.Value.ToString()

                        # Ensure host path exists
                        if (-not (Test-Path $hostPath)) {
                            try {
                                New-Item -ItemType Directory -Path $hostPath -Force | Out-Null
                                Write-Host "Created host directory for volume mount: $hostPath"
                            }
                            catch {
                                Write-Warning "Failed to create host directory for volume mount: $hostPath"
                                Write-Warning "Error: $_"
                                continue
                            }
                        }

                        # Convert to absolute path
                        $hostPath = (Resolve-Path $hostPath).Path

                        $backendVolumesMap[$hostPath] = $containerPath

                        Write-Host "Setting backend volume mount from .json file: $hostPath -> $containerPath"
                    }
                }
                elseif ($fileExtension -eq ".env" -or $fileExtension -eq "") {
                    # Process .env file
                    $envVars = Parse-EnvFile -FilePath $volFilePath

                    foreach ($containerPath in $envVars.Keys) {
                        $hostPath = $envVars[$containerPath]

                        # Ensure host path exists
                        if (-not (Test-Path $hostPath)) {
                            try {
                                New-Item -ItemType Directory -Path $hostPath -Force | Out-Null
                                Write-Host "Created host directory for volume mount: $hostPath"
                            }
                            catch {
                                Write-Warning "Failed to create host directory for volume mount: $hostPath"
                                Write-Warning "Error: $_"
                                continue
                            }
                        }

                        # Convert to absolute path
                        $hostPath = (Resolve-Path $hostPath).Path

                        # Add to volumes map
                        $backendVolumesMap[$hostPath] = $containerPath
                        Write-Host "Setting backend volume mount from .env file: $hostPath -> $containerPath"
                    }
                }
                else {
                    Write-Warning "Unsupported file format: $fileExtension. Supported formats are .json and .env"
                }
            }
            else {
                Write-Warning "Volume configuration file not found: $volFilePath"
            }
        }
        catch {
            Write-Warning "Error processing backendVolFile: $_"
            Write-Warning "Using default backend volume mounts instead."
        }
    }

    # Frontend volume files are not supported

    # Unified configuration file
    # Format: configFile='C:\path\to\config.json' or 'C:\path\to\config.env'
    if ($pp.ContainsKey('configFile')) {
        try {
            $configFilePath = $pp.configFile
            $configResult = Process-ConfigFile -FilePath $configFilePath

            # Apply backend environment variables
            foreach ($key in $configResult.BackendEnv.Keys) {
                $backendEnvVarsMap[$key] = $configResult.BackendEnv[$key]
            }

            # Apply frontend environment variables
            foreach ($key in $configResult.FrontendEnv.Keys) {
                $frontendEnvVarsMap[$key] = $configResult.FrontendEnv[$key]
            }

            # Apply backend volume mounts
            foreach ($hostPath in $configResult.BackendVol.Keys) {
                $backendVolumesMap[$hostPath] = $configResult.BackendVol[$hostPath]
            }

            Write-Host "Applied configuration from unified config file: $configFilePath"
        }
        catch {
            Write-Warning "Error processing unified config file: $_"
            Write-Warning "Using default configuration instead."
        }
    }

    # Individual environment variables (original method)
    # Format: backendEnv:NAME=VALUE (e.g., backendEnv:DEBUG=true)
    $pp.Keys | Where-Object { $_ -like "backendEnv:*" } | ForEach-Object {
        $envName = $_.Substring("backendEnv:".Length)
        $envValue = $pp[$_]
        $backendEnvVarsMap[$envName] = $envValue
        Write-Host "Setting backend environment variable: $envName=$envValue"
    }

    # Frontend environment variables
    # Format: frontendEnv:NAME=VALUE (e.g., frontendEnv:DEBUG=true)
    $pp.Keys | Where-Object { $_ -like "frontendEnv:*" } | ForEach-Object {
        $envName = $_.Substring("frontendEnv:".Length)
        $envValue = $pp[$_]
        $frontendEnvVarsMap[$envName] = $envValue
        Write-Host "Setting frontend environment variable: $envName=$envValue"
    }

    # Volume mounts for backend
    # Format: backendVol:CONTAINER_PATH=HOST_PATH (e.g., backendVol:/app/data=C:\data\backend)
    # Format with user: backendVol:CONTAINER_PATH=HOST_PATH#USER_INFO (e.g., backendVol:/app/data=C:\data\backend#1000:1000)
    $pp.Keys | Where-Object { $_ -like "backendVol:*" } | ForEach-Object {
        $containerPath = $_.Substring("backendVol:".Length)
        $volumeSpec = $pp[$_]

        # Simple host path
        $hostPath = $volumeSpec

        # Ensure host path exists
        if (-not (Test-Path $hostPath)) {
            try {
                New-Item -ItemType Directory -Path $hostPath -Force | Out-Null
                Write-Host "Created host directory for volume mount: $hostPath"
            }
            catch {
                Write-Warning "Failed to create host directory for volume mount: $hostPath"
                Write-Warning "Error: $_"
                continue
            }
        }

        # Convert to absolute path
        $hostPath = (Resolve-Path $hostPath).Path

        # Add to volumes map
        $backendVolumesMap[$hostPath] = $containerPath
        Write-Host "Setting backend volume mount: $hostPath -> $containerPath"
    }

    # Frontend volumes are not supported via individual parameters
}

# Check if specified ports are available, find alternatives if needed
try {
    if (Test-PortInUse -Port $frontendPort) {
        $originalFrontendPort = $frontendPort
        $frontendPort = Find-AvailablePort -StartPort 8080
        Write-Warning "Frontend port $originalFrontendPort is already in use. Using alternative port: $frontendPort"
    }

    if (Test-PortInUse -Port $backendPort) {
        $originalBackendPort = $backendPort
        $backendPort = Find-AvailablePort -StartPort 3000
        Write-Warning "Backend port $originalBackendPort is already in use. Using alternative port: $backendPort"
    }
}
catch {
    Write-Warning "Error checking port availability: $_"
}

Write-Host "Using ports: Frontend=$frontendPort, Backend=$backendPort"

$ErrorActionPreference = 'Continue'

try {
    # Check if running as admin
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

    # Set installation directory based on privileges
    if ($isAdmin) {
        $installDir = "$env:ProgramData\eco-edge"
    }
    else {
        $installDir = "$env:LOCALAPPDATA\eco-edge"
    }

    # Create installation directory if it doesn't exist
    if (-not (Test-Path $installDir)) {
        New-Item -ItemType Directory -Path $installDir -Force | Out-Null
    }

    # Check if Podman is installed, and install it if not
    $podmanInstalled = $false

    # First try the standard command check
    if (Get-Command podman -ErrorAction SilentlyContinue) {
        $podmanInstalled = $true
        Write-Host "Podman is already installed and available in PATH."
    }
    else {
        # Check common installation paths directly
        $podmanExePaths = @(
            "C:\ProgramData\chocoportable\lib\podman-cli\tools\podman-5.4.0\usr\bin\podman.exe",
            "C:\ProgramData\chocolatey\lib\podman-cli\tools\podman-5.4.0\usr\bin\podman.exe",
            "C:\ProgramData\chocoportable\lib\podman-cli\tools\podman.exe",
            "C:\ProgramData\chocolatey\lib\podman-cli\tools\podman.exe",
            "C:\Program Files\RedHat\Podman\podman.exe",
            "${env:ProgramFiles}\RedHat\Podman\podman.exe",
            "${env:ProgramFiles(x86)}\Podman\podman.exe",
            "${env:LOCALAPPDATA}\podman\podman.exe"
        )

        foreach ($exePath in $podmanExePaths) {
            if (Test-Path $exePath) {
                Write-Host "Found Podman at: $exePath"
                $podmanInstalled = $true
                # Create a function to use podman with full path
                function global:podman { & "$exePath" $args }
                break
            }
        }
    }

    # If Podman is not installed, install it
    if (-not $podmanInstalled) {
        Write-Host "Podman is not installed. Installing Podman..."

        try {
            # Install Podman CLI with VM support using Chocolatey
            Write-Host "Installing Podman CLI with VM support..."
            choco install podman-cli -y

            # More robust PATH refresh
            Write-Host "Refreshing environment variables..."

            # Update PATH from both Machine and User environment
            $machinePath = [System.Environment]::GetEnvironmentVariable("Path", "Machine")
            $userPath = [System.Environment]::GetEnvironmentVariable("Path", "User")
            $env:Path = "$machinePath;$userPath"

            # Add known Podman installation paths if they exist
            $podmanPaths = @(
                "C:\ProgramData\chocoportable\lib\podman-cli\tools\podman-5.4.0\usr\bin",
                "C:\ProgramData\chocolatey\lib\podman-cli\tools\podman-5.4.0\usr\bin",
                "C:\ProgramData\chocoportable\lib\podman-cli\tools",
                "C:\ProgramData\chocolatey\lib\podman-cli\tools",
                "C:\Program Files\RedHat\Podman",
                "${env:ProgramFiles}\RedHat\Podman",
                "${env:ProgramFiles(x86)}\Podman",
                "${env:LOCALAPPDATA}\podman"
            )

            foreach ($path in $podmanPaths) {
                if (Test-Path $path) {
                    Write-Host "Adding Podman path to environment: $path"
                    $env:Path = "$env:Path;$path"
                }
            }

            # Check installation again after install
            $podmanInstalled = $false

            # Try standard command check first
            if (Get-Command podman -ErrorAction SilentlyContinue) {
                $podmanInstalled = $true
            }
            else {
                # Check common installation paths directly again
                foreach ($exePath in $podmanExePaths) {
                    if (Test-Path $exePath) {
                        Write-Host "Found Podman at: $exePath"
                        $podmanInstalled = $true
                        # Create a function to use podman with full path
                        function global:podman { & "$exePath" $args }
                        break
                    }
                }
            }

            if (-not $podmanInstalled) {
                Write-Host "WARNING: Podman installation may have failed, but continuing with installation. Manual installation may be required later."
            }
            else {
                Write-Host "Podman has been successfully installed."

                # Set up Podman with VM support
                Write-Host "Configuring Podman with VM support..."
                try {
                    # Initialize the Podman machine if it doesn't exist
                    $machineExists = podman machine list | Select-String "podman-machine"
                    if (-not $machineExists) {
                        Write-Host "Initializing new Podman machine..."
                        podman machine init --cpus 2 --memory 2048 --disk-size 20
                    }

                    # Start the Podman machine
                    $machineRunning = podman machine list | Select-String "Running"
                    if (-not $machineRunning) {
                        Write-Host "Starting Podman machine..."
                        podman machine start
                    }

                    # Verify machine is running
                    $verifyRunning = podman machine list | Select-String "Running"
                    if ($verifyRunning) {
                        Write-Host "Podman VM is now running and ready to use."
                    }
                    else {
                        Write-Host "WARNING: Could not verify Podman VM is running. You may need to start it manually with 'podman machine start'."
                    }
                }
                catch {
                    Write-Host "WARNING: Error configuring Podman VM: $_"
                    Write-Host "You may need to manually set up the Podman VM with 'podman machine init' and 'podman machine start'."
                }
            }
        }
        catch {
            Write-Host "WARNING: Error during Podman installation: $_"
            Write-Host "Continuing with installation. You may need to install Podman manually later using: choco install podman-cli -y"
        }
    }

    # toolsDir is already defined at the top of the script

    # Check if Podman machine exists and is running
    try {
        $podmanMachineList = podman machine list
        $podmanMachine = $podmanMachineList | Select-String "Currently running"

        if (-not $podmanMachine) {
            # Check if machine exists but is not running
            $machineExists = $podmanMachineList | Select-String "podman-machine"

            if ($machineExists) {
                Write-Host "Starting Podman machine..."
                podman machine start
            }
            else {
                Write-Host "Initializing Podman machine..."
                podman machine init
                podman machine start
            }

            # Verify machine is running
            $podmanMachineRunning = podman machine list | Select-String "Currently running"
            if (-not $podmanMachineRunning) {
                throw "Failed to start Podman machine. Please start it manually using: podman machine start"
            }
        }

        Write-Host "Podman machine is running."
    }
    catch {
        Write-Error "Error checking Podman machine status: $_"
        throw
    }

    # Load application components
    Write-Host "Setting up Eco Edge application components..."
    $serviceImage = Join-Path $toolsDir "octopus-service.tar"
    $interfaceImage = Join-Path $toolsDir "ewp.tar"

    if (-not (Test-Path $serviceImage) -or -not (Test-Path $interfaceImage)) {
        throw "Required application components are missing. Please try reinstalling the package."
    }

    podman load -i $serviceImage
    podman load -i $interfaceImage

    # Remove any existing containers and network
    podman rm -f octopus-service ewp-edge
    podman network rm eco-edge-network

    # Create a custom network (similar to Docker Compose)
    Write-Host "Initializing Eco Edge application..."
    podman network create eco-edge-network

    # Convert environment variable maps to arrays for podman run command
    $backendEnvVars = @()
    foreach ($key in $backendEnvVarsMap.Keys) {
        $backendEnvVars += "-e"
        $backendEnvVars += "$key=$($backendEnvVarsMap[$key])"
    }

    $frontendEnvVars = @()
    foreach ($key in $frontendEnvVarsMap.Keys) {
        $frontendEnvVars += "-e"
        $frontendEnvVars += "$key=$($frontendEnvVarsMap[$key])"
    }

    # Convert volume maps to arrays for podman run command
    $backendVolumes = @()
    foreach ($hostPath in $backendVolumesMap.Keys) {
        $containerPath = $backendVolumesMap[$hostPath]
        $backendVolumes += "-v"
        $backendVolumes += "${hostPath}:${containerPath}"
    }

    # Frontend volumes are not supported

    Write-Host "Backend environment variables:"
    foreach ($key in $backendEnvVarsMap.Keys) {
        Write-Host "  $key=$($backendEnvVarsMap[$key])"
    }

    Write-Host "Frontend environment variables:"
    foreach ($key in $frontendEnvVarsMap.Keys) {
        Write-Host "  $key=$($frontendEnvVarsMap[$key])"
    }

    Write-Host "Backend volume mounts:"
    foreach ($hostPath in $backendVolumesMap.Keys) {
        Write-Host "  $hostPath -> $($backendVolumesMap[$hostPath])"
    }

    Write-Host "Note: Frontend volumes are not supported"

    # Start backend service first
    Write-Host "Starting Eco Edge backend service on port $backendPort..."
    # later change the name of docker image to octopus-service
    podman run -d --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -p "0.0.0.0:${backendPort}:9080" --user 1000:1000 $backendEnvVars $backendVolumes --name octopus-service octopus-service

    # Start frontend service that depends on backend
    Write-Host "Starting Eco Edge frontend service on port $frontendPort..."
    # later change the name of docker image to ewp-edge
    podman run -d --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -p "0.0.0.0:${frontendPort}:80" $frontendEnvVars --name ewp-edge ewp-edge

    # Verify network connectivity
    Write-Host "Verifying network connectivity..."
    podman network inspect eco-edge-network

    # Create desktop shortcut in user's desktop folder
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $shortcutFile = Join-Path $desktopPath "Eco Edge.url"

    try {
        $WshShell = New-Object -comObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut($shortcutFile)
        $Shortcut.TargetPath = "http://localhost:${frontendPort}"
        $Shortcut.Description = "Eco Edge Application"
        $Shortcut.IconLocation = "%SystemRoot%\System32\SHELL32.dll,13"
        $Shortcut.Save()
    }
    catch {
        Write-Warning "Could not create desktop shortcut: $_"
    }

    # Install system tray application
    Write-Host "Installing Eco Edge Monitor system tray application..."
    $monitorDir = Join-Path $installDir "monitor"
    if (-not (Test-Path $monitorDir)) {
        New-Item -ItemType Directory -Path $monitorDir -Force | Out-Null
    }

    # Copy system tray application files
    $monitorSourceDir = Join-Path $toolsDir "eco-edge-monitor"
    if (Test-Path $monitorSourceDir) {
        Copy-Item -Path "$monitorSourceDir\*" -Destination $monitorDir -Recurse -Force
    }
    else {
        Write-Warning "System tray application files not found. The system tray application will not be installed."
    }

    # Create startup shortcut for system tray application
    $startupPath = [Environment]::GetFolderPath("Startup")
    $monitorExePath = Join-Path $monitorDir "eco_edge_monitor.exe"
    $startupShortcutFile = Join-Path $startupPath "Eco Edge Monitor.lnk"

    if (Test-Path $monitorExePath) {
        try {
            $WshShell = New-Object -comObject WScript.Shell
            $Shortcut = $WshShell.CreateShortcut($startupShortcutFile)
            $Shortcut.TargetPath = $monitorExePath
            $Shortcut.Description = "Eco Edge Monitor"
            $Shortcut.WorkingDirectory = $monitorDir
            $Shortcut.Save()

            # Kill any existing instances of the application
            try {
                $processes = Get-Process -Name "eco_edge_monitor" -ErrorAction SilentlyContinue
                if ($processes) {
                    $processes | ForEach-Object { $_.Kill() }
                    Write-Host "Stopped existing Eco Edge Monitor instances."
                    Start-Sleep -Seconds 1 # Give it time to fully terminate
                }
            }
            catch {
                Write-Warning "Could not stop existing Eco Edge Monitor processes: $_"
            }

            # Start the system tray application
            Start-Process -FilePath $monitorExePath -WorkingDirectory $monitorDir
            Write-Host "Eco Edge Monitor system tray application has been installed and started."

            # Wait a moment to ensure the application has time to initialize
            Start-Sleep -Seconds 2

            # Check if the application is running
            $isRunning = Get-Process -Name "eco_edge_monitor" -ErrorAction SilentlyContinue
            if ($isRunning) {
                Write-Host "Eco Edge Monitor is running successfully."
            }
            else {
                Write-Warning "Eco Edge Monitor may not have started correctly. Please check the application manually."
            }
        }
        catch {
            Write-Warning "Could not create startup shortcut for system tray application: $_"
        }
    }

    # Save installation info for uninstall
    $installInfo = @{
        InstallPath  = $installDir
        IsAdmin      = $isAdmin
        FrontendPort = $frontendPort
        BackendPort  = $backendPort
        MonitorPath  = $monitorDir
    } | ConvertTo-Json

    $installInfoPath = Join-Path $installDir "install-info.json"
    $installInfo | Out-File -FilePath $installInfoPath -Encoding UTF8

    Write-Host "Eco Edge has been successfully installed!"
    Write-Host "You can access the application at http://localhost:${frontendPort}"
    Write-Host "The backend API is available at http://localhost:${backendPort}/api/hello"
    Write-Host "The system tray monitor is running in the background."
}
catch {
    Write-Error "Installation failed: $_"
    throw
}