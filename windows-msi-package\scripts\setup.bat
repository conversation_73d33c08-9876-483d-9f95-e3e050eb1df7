@echo off
echo ====================================================
echo Eco Edge Agent Installation Setup
echo ====================================================
echo.
echo This will complete the installation of the Eco Edge Agent.
echo The process will:
echo  1. Install required dependencies
echo  2. Set up the Flutter application
echo  3. Configure the system
echo.
echo Press any key to continue...
pause > nul

:: Run the PowerShell installation script
powershell.exe -NoProfile -ExecutionPolicy Bypass -File "%~dp0install.ps1"

echo.
if %ERRORLEVEL% NEQ 0 (
  echo Installation failed with error code %ERRORLEVEL%
  echo Please contact technical support.
  pause
  exit /b %ERRORLEVEL%
)

echo Installation completed successfully.
echo You can now use Eco Edge Agent.
echo.
echo Press any key to exit...
pause > nul
exit /b 0
