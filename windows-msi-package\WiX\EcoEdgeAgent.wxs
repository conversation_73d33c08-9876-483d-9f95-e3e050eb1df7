<?xml version="1.0" encoding="utf-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs"
     xmlns:util="http://wixtoolset.org/schemas/v4/wxs/util"
     xmlns:ui="http://wixtoolset.org/schemas/v4/wxs/ui">
  <Package 
    Name="Eco Edge Agent" 
    Language="1033" 
    Version="1.0.0.0" 
    Manufacturer="YourCompany" 
    UpgradeCode="D2B5C5A2-7F7B-4E3B-8D0A-1F8B6B2D2C1E"
    InstallerVersion="500" 
    Compressed="yes">
    
    <!-- Define properties -->
    <Property Id="CONFIGFILEPATH" Secure="yes" />
    <Property Id="WIXUI_INSTALLDIR" Value="INSTALLFOLDER" />
    <Property Id="ECOEDGEDIR" Secure="yes" />
    
    <!-- Simple basic UI - known to work with WiX 5.0 -->
    <UI>
      <Property Id="DefaultUIFont" Value="WixUI_Font_Normal" />
      <TextStyle Id="WixUI_Font_Normal" FaceName="Tahoma" Size="8" />
      <TextStyle Id="WixUI_Font_Bigger" FaceName="Tahoma" Size="12" />
      <TextStyle Id="WixUI_Font_Title" FaceName="Tahoma" Size="9" Bold="yes" />
    </UI>
    
    <!-- Set ECOEDGEDIR property dynamically -->
    <CustomAction Id="SetECOEDGEDIR" Property="ECOEDGEDIR" Value="%ProgramData%\EcoEdgeAgent\monitor" Execute="immediate" />
    
    <!-- Define custom action to create a scheduled task to run the installer with elevated privileges -->
    <Property Id="POWERSHELLEXE" Value="powershell.exe" />
    <CustomAction Id="SetupPostInstall" Property="POWERSHELLEXE" Execute="immediate" ExeCommand="-ExecutionPolicy Bypass -NoProfile -File &quot;[INSTALLFOLDER]scripts\post_install.ps1&quot; -InstallPath &quot;[INSTALLFOLDER]&quot;" Return="asyncNoWait" />
    
    <MediaTemplate EmbedCab="yes" />
    
    <!-- Define directory structure using StandardDirectory for WiX 5.0 -->
    <StandardDirectory Id="ProgramFiles64Folder">
      <Directory Id="INSTALLFOLDER" Name="EcoEdgeAgent">
        <Directory Id="DockerTarsDir" Name="docker_tars" />
        <Directory Id="ScriptsDir" Name="scripts" />
        <Directory Id="EcoEdgeSourceDir" Name="eco-edge" />
      </Directory>
    </StandardDirectory>
    
    <StandardDirectory Id="CommonAppDataFolder">
      <Directory Id="AppDataEcoEdge" Name="EcoEdgeAgent">
        <Directory Id="EcoEdgeDir" Name="monitor" />
      </Directory>
    </StandardDirectory>
    
    <StandardDirectory Id="ProgramMenuFolder" />
    <StandardDirectory Id="DesktopFolder" />

    <!-- Define components -->
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component Id="MainComponent" Guid="*">
        <File Id="InstallPs1" Source="..\scripts\install.ps1" KeyPath="yes" />
        <RegistryValue Root="HKLM" Key="Software\EcoEdgeAgent" Name="installed" Type="integer" Value="1" />
      </Component>
    </ComponentGroup>

    <!-- Docker images -->
    <DirectoryRef Id="DockerTarsDir">
      <Component Id="OctopusServiceImage" Guid="*">
        <File Id="OctopusServiceTar" Source="..\docker_tars\octopus-service.tar" KeyPath="yes" />
      </Component>
      <Component Id="EwpImage" Guid="*">
        <File Id="EwpTar" Source="..\docker_tars\ewp.tar" KeyPath="yes" />
      </Component>
    </DirectoryRef>
    
    <!-- Flutter application source files -->
    <DirectoryRef Id="EcoEdgeSourceDir">
      <Component Id="EcoEdgeSourceFiles" Guid="*">
        <CreateFolder />
        <util:RemoveFolderEx On="uninstall" Property="INSTALLFOLDER" />
        <RegistryValue Root="HKLM" Key="Software\EcoEdgeAgent" Name="ecoEdgeSourceInstalled" Type="integer" Value="1" KeyPath="yes" />
      </Component>
    </DirectoryRef>
    
    <!-- Flutter application components -->
    <DirectoryRef Id="AppDataEcoEdge">
      <Component Id="FlutterAppFiles" Guid="*">
        <CreateFolder />
        <util:RemoveFolderEx On="uninstall" Property="CommonAppDataFolder" />
        <RegistryValue Root="HKLM" Key="Software\EcoEdgeAgent" Name="ecoEdgeInstalled" Type="integer" Value="1" KeyPath="yes" />
      </Component>
    </DirectoryRef>

    <!-- Note: This Feature element will be replaced by the one at the end of the file -->

    <!-- Using built-in InstallDir UI for now, as custom dialogs need to be completely reworked for WiX 5.0 -->
    <!-- Custom dialog work is complex and requires extensive testing with WiX 5.0 -->
    
    <!-- TEMPORARILY COMMENTED OUT: Desktop shortcut -->
    <!--
    <DirectoryRef Id="DesktopFolder">
      <Component Id="DesktopShortcut" Guid="*">
        <Shortcut Id="SetupShortcut"
                  Name="Complete Eco Edge Agent Setup"
                  Description="Run to complete Eco Edge Agent installation"
                  Target="[INSTALLFOLDER]scripts\setup.bat"
                  WorkingDirectory="[INSTALLFOLDER]scripts" />
        <RegistryValue Root="HKCU" Key="Software\EcoEdgeAgent" Name="desktop_shortcut" Type="integer" Value="1" KeyPath="yes" />
      </Component>
    </DirectoryRef>
    -->
    
    <!-- TEMPORARILY COMMENTED OUT: Start Menu entry -->
    <!--
    <DirectoryRef Id="ProgramMenuFolder">
      <Directory Id="ApplicationProgramsFolder" Name="Eco Edge Agent">
        <Component Id="ApplicationShortcut" Guid="*">
          <Shortcut Id="SetupMenuShortcut"
                  Name="Complete Eco Edge Agent Setup"
                  Description="Run to complete Eco Edge Agent installation"
                  Target="[INSTALLFOLDER]scripts\setup.bat"
                  WorkingDirectory="[INSTALLFOLDER]scripts" />
          <RemoveFolder Id="CleanUpShortCut" On="uninstall" />
          <RegistryValue Root="HKCU" Key="Software\EcoEdgeAgent" Name="installed" Type="integer" Value="1" KeyPath="yes" />
        </Component>
      </Directory>
    </DirectoryRef>
    -->
    
    <InstallExecuteSequence>
      <!-- Set ECOEDGEDIR property early in the sequence -->
      <Custom Action="SetECOEDGEDIR" Before="InstallFiles" Condition="1" />
      <!-- Run post-install script after installation is complete -->
      <Custom Action="SetupPostInstall" After="InstallFinalize" Condition="NOT Installed" />
    </InstallExecuteSequence>
    
    <!-- Define features -->
    <Feature Id="ProductFeature" Title="Eco Edge Agent" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
      <ComponentRef Id="OctopusServiceImage" />
      <ComponentRef Id="EwpImage" />
      <ComponentRef Id="EcoEdgeSourceFiles" />
      <ComponentRef Id="FlutterAppFiles" />
      <!-- Temporarily commented out shortcut components -->
      <!-- <ComponentRef Id="ApplicationShortcut" /> -->
      <!-- <ComponentRef Id="DesktopShortcut" /> -->
    </Feature>
  </Package>
</Wix>
