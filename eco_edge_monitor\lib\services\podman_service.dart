import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:process_run/process_run.dart';
import '../models/service_status.dart';
import '../models/constants/utils.dart';

class PodmanService {
  final shell = Shell();
  String? _podmanPath;

  PodmanService() {
    _initPodmanPath();
  }

  Future<void> _initPodmanPath() async {
    try {
      // First check if podman is available in PATH by running a simple command
      final versionResult = await shell.run('podman --version');
      if (versionResult.isNotEmpty && versionResult.first.exitCode == 0) {
        // Podman is available in PATH, so we can use it directly
        _podmanPath = null; // null means use 'podman' directly from PATH
        print('Found Podman in PATH, using system Podman');
        return;
      }
    } catch (e) {
      print('Podman not found in PATH, checking specific locations');
    }

    // Check common installation paths directly
    final podmanExePaths = [
      "C:\\ProgramData\\chocoportable\\lib\\podman-cli\\tools\\podman-5.4.0\\usr\\bin\\podman.exe",
      "C:\\ProgramData\\chocolatey\\lib\\podman-cli\\tools\\podman-5.4.0\\usr\\bin\\podman.exe",
      "C:\\ProgramData\\chocoportable\\lib\\podman-cli\\tools\\podman.exe",
      "C:\\ProgramData\\chocolatey\\lib\\podman-cli\\tools\\podman.exe",
      "C:\\Program Files\\RedHat\\Podman\\podman.exe",
      "${Platform.environment['ProgramFiles']}\\RedHat\\Podman\\podman.exe",
      "${Platform.environment['ProgramFiles(x86)']}\\Podman\\podman.exe",
      "${Platform.environment['LOCALAPPDATA']}\\podman\\podman.exe",
    ];

    for (final exePath in podmanExePaths) {
      if (await File(exePath).exists()) {
        _podmanPath = exePath;
        print('Found Podman at: $_podmanPath');
        break;
      }
    }

    if (_podmanPath == null) {
      print('Warning: Podman executable not found in known locations');
    }
  }

  String _getPodmanCommand(String command) {
    if (_podmanPath != null) {
      return '& "$_podmanPath" $command';
    }
    return 'podman $command';
  }

  Future<List<Map<String, dynamic>>> getContainers() async {
    try {
      final result = await shell.run(_getPodmanCommand('ps --format json'));
      final output = result.outText.trim();

      if (output.isEmpty) {
        return [];
      }

      return List<Map<String, dynamic>>.from(jsonDecode(output));
    } catch (e) {
      print('Error getting containers: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>?> getContainerStats(String containerId) async {
    try {
      final result = await shell.run(
        _getPodmanCommand('stats --no-stream --format json $containerId'),
      );
      final output = result.outText.trim();

      if (output.isEmpty) {
        return null;
      }

      final stats = List<Map<String, dynamic>>.from(jsonDecode(output));
      return stats.isNotEmpty ? stats.first : null;
    } catch (e) {
      print('Error getting container stats: $e');
      return null;
    }
  }

  Future<void> startContainer(String containerName) async {
    try {
      await shell.run(_getPodmanCommand('start $containerName'));
    } catch (e) {
      print('Error starting container: $e');
      rethrow;
    }
  }

  Future<void> stopContainer(String containerName) async {
    try {
      await shell.run(_getPodmanCommand('stop $containerName'));
    } catch (e) {
      print('Error stopping container: $e');
      rethrow;
    }
  }

  Future<void> restartContainer(String containerName) async {
    try {
      await shell.run(_getPodmanCommand('restart $containerName'));
    } catch (e) {
      print('Error restarting container: $e');
      rethrow;
    }
  }

  Future<bool> ensurePodmanMachineRunning() async {
    try {
      // Check if Podman machine is running
      final result = await shell.run(_getPodmanCommand('machine list'));
      final isRunning = result.outText.contains('Currently running');

      if (!isRunning) {
        // Try to start the Podman machine
        print('Starting Podman machine...');
        await shell.run(_getPodmanCommand('machine start'));

        // Verify it's now running
        final verifyResult = await shell.run(_getPodmanCommand('machine list'));
        return verifyResult.outText.contains('Currently running');
      }

      return true;
    } catch (e) {
      print('Error ensuring Podman machine is running: $e');
      return false;
    }
  }

  Future<bool> ensureContainersRunning() async {
    try {
      // Check all containers, including stopped ones
      final result = await shell.run(_getPodmanCommand('ps -a --format json'));
      final output = result.outText.trim();

      if (output.isEmpty) {
        print('No containers found');
        return false;
      }

      final containers = List<Map<String, dynamic>>.from(jsonDecode(output));
      bool startedAny = false;

      for (final container in containers) {
        final name = container['Names'][0] as String;
        final state = container['State'] as String;

        if ((name == EcoEdgeConstants.frontendContainerName ||
                name == EcoEdgeConstants.backendContainerName) &&
            state.toLowerCase() != 'running') {
          print('Starting container: $name');
          await startContainer(name);
          startedAny = true;
        }
      }

      return startedAny;
    } catch (e) {
      print('Error ensuring containers are running: $e');
      return false;
    }
  }

  Future<EcoEdgeStatus> getEcoEdgeStatus([bool autoStartContainers = true]) async {
    // First ensure Podman machine is running
    final machineRunning = await ensurePodmanMachineRunning();

    if (machineRunning && autoStartContainers) {
      // Then ensure containers are running if they exist
      await ensureContainersRunning();
    }

    final containers = await getContainers();

    ServiceStatus frontendStatus = ServiceStatus(
      name: EcoEdgeConstants.frontendDisplayName,
      state: ServiceState.unknown,
      port: 'Unknown',
    );

    ServiceStatus backendStatus = ServiceStatus(
      name: EcoEdgeConstants.backendDisplayName,
      state: ServiceState.unknown,
      port: 'Unknown',
    );

    for (final container in containers) {
      final name = container['Names'][0] as String;
      final ports = container['Ports'] as List<dynamic>;
      String portMapping = 'Unknown';

      if (ports.isNotEmpty) {
        final port = ports[0] as Map<String, dynamic>;
        portMapping = '${port['host_port']}:${port['container_port']}';
      }

      if (name == EcoEdgeConstants.frontendContainerName) {
        final stats = await getContainerStats(container['Id']);
        final cpuUsage =
            stats != null ? _parseCpuPercentage(stats['cpu_percent']) : 0.0;
        final memoryUsage =
            stats != null ? _parseMemoryUsage(stats['mem_usage']) : 0.0;

        frontendStatus = ServiceStatus(
          name: EcoEdgeConstants.frontendDisplayName,
          state: ServiceState.running,
          port: portMapping,
          containerName: name,
          containerImage: container['Image'],
          containerID: container['Id'],
          cpuUsage: cpuUsage,
          memoryUsage: memoryUsage,
        );
      } else if (name == EcoEdgeConstants.backendContainerName) {
        final stats = await getContainerStats(container['Id']);
        final cpuUsage =
            stats != null ? _parseCpuPercentage(stats['cpu_percent']) : 0.0;
        final memoryUsage =
            stats != null ? _parseMemoryUsage(stats['mem_usage']) : 0.0;
        backendStatus = ServiceStatus(
          name: EcoEdgeConstants.backendDisplayName,
          state: ServiceState.running,
          port: portMapping,
          containerName: name,
          containerImage: container['Image'],
          containerID: container['Id'],
          cpuUsage: cpuUsage,
          memoryUsage: memoryUsage,
        );
      }
    }
    return EcoEdgeStatus(
      frontendService: frontendStatus,
      backendService: backendStatus,
      lastUpdated: DateTime.now(),
    );
  }

  double _parseCpuPercentage(String cpuPerc) {
    try {
      return double.parse(cpuPerc.replaceAll('%', ''));
    } catch (e) {
      return 0.0;
    }
  }

  double _parseMemoryUsage(String memUsage) {
    try {
      final parts = memUsage.split(' / ');
      if (parts.isNotEmpty) {
        final usagePart = parts[0];
        if (usagePart.contains('MiB')) {
          return double.parse(usagePart.replaceAll('MiB', '').trim());
        } else if (usagePart.contains('GiB')) {
          return double.parse(usagePart.replaceAll('GiB', '').trim()) * 1024;
        }
      }
      return 0.0;
    } catch (e) {
      return 0.0;
    }
  }
}
