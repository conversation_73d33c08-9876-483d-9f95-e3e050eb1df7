# build.ps1
# PowerShell script to build the MSI installer from WiX source files

# Configuration variables
$productName = "ESXPEdgeAgent"
$outputDir = "output" # Relative path without .\ prefix
# Auto-detect WiX Toolset path
$wixBinPath = $null
$wixPaths = @(
    "C:\Program Files (x86)\WiX Toolset v3.11\bin",
    "C:\Program Files\WiX Toolset v3.11\bin",
    "C:\Program Files (x86)\WiX Toolset v3.14\bin",
    "C:\Program Files\WiX Toolset v3.14\bin"
)

foreach ($path in $wixPaths) {
    if (Test-Path $path) {
        $wixBinPath = $path
        break
    }
}

if (-not $wixBinPath) {
    Write-Host "Error: WiX Toolset not found. Please install WiX Toolset v3.x or update the script with the correct path." -ForegroundColor Red
    exit 1
}

Write-Host "Using WiX Toolset from: $wixBinPath" -ForegroundColor Green


# Create output directory if it doesn't exist
try {
    # Make sure we're using an absolute path for the output directory
    if (-not [System.IO.Path]::IsPathRooted($outputDir)) {
        $outputDir = Join-Path -Path $PWD -ChildPath $outputDir
    }

    Write-Host "Using output directory: $outputDir" -ForegroundColor Cyan

    if (-not (Test-Path $outputDir)) {
        New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
        Write-Host "Created output directory: $outputDir"
    }
}
catch {
    Write-Host "Error creating output directory: $_" -ForegroundColor Red
    Write-Host "Build failed. Please check permissions and try again." -ForegroundColor Red
    exit 1
}

# Function to check if a command exists
function Test-CommandExists {
    param ($command)
    $exists = $null -ne (Get-Command $command -ErrorAction SilentlyContinue)
    return $exists
}

# Check if WiX tools are available
if (-not (Test-Path "$wixBinPath\candle.exe")) {
    Write-Host "Error: WiX Toolset not found at $wixBinPath" -ForegroundColor Red
    Write-Host "Please install WiX Toolset v3.x or update the wixBinPath variable in this script." -ForegroundColor Red
    exit 1
}

# Check for custom action DLL
$customActionDllPath = "CustomAction\x64\FileBrowse.dll"
if (Test-Path $customActionDllPath) {
    Write-Host "Found custom action DLL at: $customActionDllPath" -ForegroundColor Green
}
else {
    Write-Host "Custom action DLL not found at: $customActionDllPath" -ForegroundColor Yellow
    Write-Host "The build will continue, but the file browsing functionality may not work properly." -ForegroundColor Yellow
    Write-Host "You will need Visual Studio to build the custom action DLL if needed." -ForegroundColor Yellow
    exit 1
}

# Create placeholder bitmaps if they don't exist
if (-not (Test-Path "banner.bmp")) {
    Write-Host "Creating placeholder banner.bmp..." -ForegroundColor Yellow

    # Create a very simple BMP file (1x1 pixel)
    $bannerBytes = [byte[]]@(
        0x42, 0x4D, 0x3A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x28, 0x00,
        0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x18, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x00
    )

    try {
        [System.IO.File]::WriteAllBytes("banner.bmp", $bannerBytes)
        Write-Host "Created banner.bmp" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to create banner.bmp: $_" -ForegroundColor Red
        Write-Host "Please create it manually." -ForegroundColor Red
    }
}

if (-not (Test-Path "dialog.bmp")) {
    Write-Host "Creating placeholder dialog.bmp..." -ForegroundColor Yellow

    # Create a very simple BMP file (1x1 pixel)
    $dialogBytes = [byte[]]@(
        0x42, 0x4D, 0x3A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x00, 0x28, 0x00,
        0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x00, 0x18, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x00
    )

    try {
        [System.IO.File]::WriteAllBytes("dialog.bmp", $dialogBytes)
        Write-Host "Created dialog.bmp" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to create dialog.bmp: $_" -ForegroundColor Red
        Write-Host "Please create it manually." -ForegroundColor Red
    }
}

# Compile WiX source files
Write-Host "Compiling WiX source files..." -ForegroundColor Cyan

# Create a simpler approach - let's create a basic MSI without requiring WiX
Write-Host "Creating a basic MSI file for demonstration purposes..." -ForegroundColor Yellow

# Create output directory if it doesn't exist
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
}

# Check if WiX is installed and available
$wixInstalled = $false
$candlePath = Join-Path -Path $wixBinPath -ChildPath "candle.exe"
$lightPath = Join-Path -Path $wixBinPath -ChildPath "light.exe"
$heatPath = Join-Path -Path $wixBinPath -ChildPath "heat.exe"

if (Test-Path $candlePath -PathType Leaf) {
    $wixInstalled = $true
    Write-Host "WiX Toolset found at: $wixBinPath" -ForegroundColor Green

    try {
        # Add WiX bin directory to the path temporarily
        $env:PATH = "$wixBinPath;$env:PATH"

        # Copy banner.bmp and dialog.bmp to the WiX directory
        $wixUIExtDir = Join-Path -Path $wixBinPath -ChildPath "..\SDK\themes\wixstdba"
        if (Test-Path $wixUIExtDir) {
            Write-Host "Copying banner.bmp and dialog.bmp to WiX themes directory..." -ForegroundColor Cyan
            try {
                Copy-Item -Path "banner.bmp" -Destination $wixUIExtDir -Force
                Copy-Item -Path "dialog.bmp" -Destination $wixUIExtDir -Force
                Write-Host "Successfully copied bitmap files" -ForegroundColor Green
            }
            catch {
                Write-Host "Warning: Could not copy bitmap files to WiX directory: $_" -ForegroundColor Yellow
            }
        }

        # Generate WiX fragment for flutter_assets directory using heat.exe
        if (Test-Path $heatPath -PathType Leaf) {
            Write-Host "Generating WiX fragment for flutter_assets directory..." -ForegroundColor Cyan
            $flutterAssetsDir = "eco-edge\data\flutter_assets"
            if (Test-Path $flutterAssetsDir) {
                # Use absolute path for the flutter_assets directory
                $absoluteFlutterAssetsDir = Join-Path -Path $PWD -ChildPath $flutterAssetsDir

                # Create a modified heat command that uses the correct paths
                & $heatPath dir $absoluteFlutterAssetsDir -dr FlutterAssetsSourceDir -cg FlutterAssetsGroup -gg -scom -sreg -sfrag -srd -var "var.SourceDir" -out "$outputDir\FlutterAssets.wxs"
                
                # Wait a moment to ensure the file is written
                Start-Sleep -Seconds 1

                if ($LASTEXITCODE -ne 0) {
                    Write-Host "Error: Failed to generate WiX fragment for flutter_assets directory." -ForegroundColor Red
                    Write-Host "Build failed. Please check the flutter_assets directory and try again." -ForegroundColor Red
                    exit 1
                }
                else {
                    Write-Host "Successfully generated WiX fragment for flutter_assets directory" -ForegroundColor Green

                    # Fix the paths in the generated WXS file
                    try {
                        $wxsContent = Get-Content -Path "$outputDir\FlutterAssets.wxs" -Raw
                        
                        # Get the absolute path to the flutter_assets directory and escape it for regex
                        $absolutePath = [regex]::Escape("$(Join-Path -Path $PWD -ChildPath "eco-edge\data\flutter_assets\")") 
                        
                        # Replace the absolute path with the relative path
                        $wxsContent = $wxsContent -replace $absolutePath, "eco-edge\data\flutter_assets\"
                        
                        # Fix the Source attribute to use the correct variable
                        $wxsContent = $wxsContent -replace 'Source="\$\(var\.SourceDir\)\\([^"]+)"', 'Source="$(var.SourceDir)\eco-edge\data\flutter_assets\$1"'
                        
                        # Write the fixed content back to the file
                        $wxsContent | Set-Content -Path "$outputDir\FlutterAssets.wxs" -Force
                        
                        Write-Host "Successfully fixed paths in FlutterAssets.wxs" -ForegroundColor Green
                    }
                    catch {
                        Write-Host "Error: Failed to fix paths in FlutterAssets.wxs: $_" -ForegroundColor Red
                        Write-Host "Build failed. Please check file permissions and try again." -ForegroundColor Red
                        exit 1
                    }

                    # Compile the generated fragment
                    & $candlePath -ext WixUIExtension "$outputDir\FlutterAssets.wxs" -dSourceDir="$PWD" -out "$outputDir\"

                    if ($LASTEXITCODE -ne 0) {
                        Write-Host "Error: Failed to compile flutter_assets fragment." -ForegroundColor Red
                        Write-Host "Build failed. Please check the flutter_assets directory and try again." -ForegroundColor Red
                        exit 1
                    }
                    else {
                        Write-Host "Successfully compiled flutter_assets fragment" -ForegroundColor Green
                    }
                }
            }
            else {
                Write-Host "Warning: flutter_assets directory not found. Continuing without it..." -ForegroundColor Yellow
            }
        }
        else {
            Write-Host "Warning: WiX Heat tool not found. Continuing without flutter_assets files..." -ForegroundColor Yellow
        }

        # Compile the WiX source files
        Write-Host "Running candle.exe..." -ForegroundColor Cyan
        & $candlePath -ext WixUIExtension -ext WixUtilExtension Product.wxs WixUI_Custom.wxs FileBrowseDlg.wxs InstallDirDlgCustom.wxs -out "$outputDir\"

        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error: Failed to compile WiX source files." -ForegroundColor Red
            Write-Host "Build failed. Please check the WiX source files and try again." -ForegroundColor Red
            exit 1
        }

        # Link the object files to create the MSI
        Write-Host "Running light.exe..." -ForegroundColor Cyan

        # Check if the MSI file exists and try to delete it first
        $msiPath = "$outputDir\$productName.msi"
        if (Test-Path $msiPath) {
            try {
                Write-Host "Removing existing MSI file: $msiPath" -ForegroundColor Cyan
                Remove-Item -Path $msiPath -Force
            }
            catch {
                Write-Host "Warning: Could not remove existing MSI file: $_" -ForegroundColor Yellow
                Write-Host "Will try to use a different output filename" -ForegroundColor Yellow
                $productName = "$productName-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
                $msiPath = "$outputDir\$productName.msi"
                Write-Host "New MSI filename: $msiPath" -ForegroundColor Cyan
            }
        }

        # Check if FlutterAssets.wixobj exists and include it in the linking
        $flutterAssetsObj = "$outputDir\FlutterAssets.wixobj"
        if (Test-Path $flutterAssetsObj) {
            & $lightPath -ext WixUIExtension -ext WixUtilExtension -sval -v "$outputDir\Product.wixobj" "$outputDir\WixUI_Custom.wixobj" "$outputDir\FileBrowseDlg.wixobj" "$outputDir\InstallDirDlgCustom.wixobj" "$flutterAssetsObj" -out "$msiPath" > "$outputDir\light_verbose.log" 2>&1
        }
        else {
            & $lightPath -ext WixUIExtension -ext WixUtilExtension -sval -v "$outputDir\Product.wixobj" "$outputDir\WixUI_Custom.wixobj" "$outputDir\FileBrowseDlg.wixobj" "$outputDir\InstallDirDlgCustom.wixobj" -out "$msiPath" > "$outputDir\light_verbose.log" 2>&1
        }

        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error: Failed to link WiX object files." -ForegroundColor Red
            Write-Host "Build failed. Please check the WiX source files and try again." -ForegroundColor Red
            exit 1
        }
    }
    catch {
        Write-Host "Error during WiX compilation: $_" -ForegroundColor Red
        Write-Host "Build failed. Please check the error message and try again." -ForegroundColor Red
        exit 1
    }
}

# If WiX is not installed, exit with an error
if (-not $wixInstalled) {
    Write-Host "Error: WiX Toolset is not installed or not found at $wixBinPath" -ForegroundColor Red
    Write-Host "Build failed. Please install WiX Toolset v3.x and try again." -ForegroundColor Red
    exit 1
}

# If MSI was not created, exit with an error
if (-not (Test-Path "$outputDir\$productName.msi")) {
    Write-Host "Error: MSI file was not created at $outputDir\$productName.msi" -ForegroundColor Red
    Write-Host "Build failed. Please check the error messages above and try again." -ForegroundColor Red
    exit 1
}

# Verify the MSI file exists and report success
if (Test-Path $msiPath) {
    Write-Host "Successfully created $msiPath" -ForegroundColor Green
    Write-Host "Build completed successfully!" -ForegroundColor Green
}
else {
    # This should never happen due to the previous check, but just in case
    Write-Host "Error: MSI file was not created at $msiPath" -ForegroundColor Red
    Write-Host "Build failed. Please check the error messages above and try again." -ForegroundColor Red
    exit 1
}