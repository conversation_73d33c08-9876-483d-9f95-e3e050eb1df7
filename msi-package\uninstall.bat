@echo off
:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Administrator privileges required.
    echo Attempting to elevate...
    powershell -Command "Start-Process '%~dpnx0' -Verb RunAs"
    exit /b
)

echo Eco Edge Agent Uninstallation
echo ===========================
echo.
echo This script will uninstall the Eco Edge Agent application.
echo Running with administrator privileges.
echo.

echo Choose an uninstallation method:
echo 1. Run uninstall script only (manual uninstall)
echo 2. Run MSI uninstaller (complete uninstall)
echo 3. Force uninstall (use if other methods fail)
echo.
set /p UNINSTALL_METHOD="Enter your choice (1, 2, or 3): "

if "%UNINSTALL_METHOD%"=="2" goto RunMsiUninstaller
if "%UNINSTALL_METHOD%"=="3" goto ForceUninstall

:ManualUninstall
echo Running Eco Edge Agent uninstallation script...
echo This will remove all components following these steps:
echo 1. Remove the startup link
echo 2. Kill and remove the Flutter application
echo 3. Stop and remove all containers
echo 4. Remove all images
echo 5. Remove all data and configuration files
echo 6. Remove all registry entries and startup configurations
echo.

set LOG_FILE=%TEMP%\eco_edge_uninstall_log.txt
echo Log file will be created at: %LOG_FILE%
echo.

:: Run the PowerShell uninstall script with elevated privileges
:: This script follows the same 6 steps as the force uninstall option
powershell.exe -NoProfile -ExecutionPolicy Bypass -Command "& { Start-Process PowerShell -ArgumentList '-NoProfile -ExecutionPolicy Bypass -File \"%~dp0scripts\uninstall.ps1\"' -Verb RunAs -Wait }"
echo.
echo Uninstallation script completed.

if exist "%LOG_FILE%" (
    echo.
    echo Log file created at: %LOG_FILE%
    echo Opening log file...
    start notepad "%LOG_FILE%"
)

goto End

:RunMsiUninstaller
echo.
echo Running MSI uninstaller...
echo This will also follow the same 6-step uninstallation process:
echo 1. Remove the startup link
echo 2. Kill and remove the Flutter application
echo 3. Stop and remove all containers
echo 4. Remove all images
echo 5. Remove all data and configuration files
echo 6. Remove all registry entries and startup configurations
echo.
set MSI_FOUND=0

for /f "delims=" %%i in ('dir /b /s "%ProgramData%\Package Cache\*EcoEdgeAgent*.msi" 2^>nul') do (
    echo Found MSI: %%i
    msiexec /x "%%i" /qb
    set MSI_FOUND=1
    goto MsiFound
)

echo No MSI found in Package Cache. Checking Windows Installer database...
echo.
for /f "delims=" %%i in ('wmic product where "name like '%%Eco Edge%%'" get IdentifyingNumber ^| findstr /r "{"') do (
    echo Found product: %%i
    msiexec /x %%i /qb
    set MSI_FOUND=1
    goto MsiFound
)

if %MSI_FOUND%==0 (
    echo No MSI installer found. Would you like to try force uninstall? (Y/N)
    set /p FORCE_UNINSTALL="Enter Y or N: "
    if /i "%FORCE_UNINSTALL%"=="Y" goto ForceUninstall
    echo Please uninstall the application from Control Panel.
    goto End
)

:MsiFound
echo MSI uninstaller launched. Please follow the on-screen instructions.
echo.
echo After MSI uninstallation completes, would you like to run the cleanup script to ensure all components are removed? (Y/N)
set /p RUN_CLEANUP="Enter Y or N: "
if /i "%RUN_CLEANUP%"=="Y" goto ForceUninstall
goto End

:ForceUninstall
echo.
echo Running force uninstall to remove all components...
echo.

:: Step 1: Remove the startup link
echo Step 1: Removing startup links...
if exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk" (
    echo Removing startup shortcut...
    del /f /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk"
)

:: Look for other related shortcuts in startup folder
echo Looking for other related shortcuts...
for /f "delims=" %%i in ('dir /b "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\*eco*edge*.lnk" 2^>nul') do (
    echo Found related shortcut: %%i
    del /f /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\%%i"
)

:: Step 2: Kill and remove the Flutter application
echo Step 2: Killing and removing the Flutter application...
echo Stopping any running instances of the application...
taskkill /f /im eco_edge_monitor.exe >nul 2>&1
taskkill /f /im ecoedgemonitor.exe >nul 2>&1
taskkill /f /im eco-edge-monitor.exe >nul 2>&1
taskkill /f /im ecoedge.exe >nul 2>&1

:: Remove application directories
echo Removing application directories...
set INSTALL_DIR=%~dp0..
echo Installation directory: %INSTALL_DIR%

if exist "%INSTALL_DIR%\monitor" (
    echo Removing monitor directory...
    rmdir /s /q "%INSTALL_DIR%\monitor"
)

if exist "%INSTALL_DIR%\eco-edge" (
    echo Removing eco-edge directory...
    rmdir /s /q "%INSTALL_DIR%\eco-edge"
)

:: Step 3: Stop and remove all containers
echo Step 3: Stopping and removing all containers...
where podman >nul 2>&1
if %errorLevel% equ 0 (
    echo Podman is installed. Proceeding with container cleanup...

    :: First try to stop and remove specific containers
    echo Stopping and removing specific containers...
    podman stop octopus-service ewp-edge >nul 2>&1
    podman rm -f octopus-service ewp-edge >nul 2>&1

    :: Then stop and remove all containers
    echo Stopping all containers...
    for /f "tokens=1" %%i in ('podman ps -q 2^>nul') do (
        echo Stopping container: %%i
        podman stop %%i >nul 2>&1
    )

    echo Removing all containers...
    for /f "tokens=1" %%i in ('podman ps -a -q 2^>nul') do (
        echo Removing container: %%i
        podman rm -f %%i >nul 2>&1
    )

    :: Remove network
    echo Removing eco-edge-network...
    podman network rm eco-edge-network >nul 2>&1
) else (
    echo Podman not found. Skipping container cleanup.
)

:: Step 4: Remove all images
echo Step 4: Removing all container images...
where podman >nul 2>&1
if %errorLevel% equ 0 (
    :: First try to remove specific images
    echo Removing specific images...
    podman rmi -f octopus-service ewp-edge >nul 2>&1

    :: Then remove all images
    echo Removing all images...
    for /f "tokens=1" %%i in ('podman images -q 2^>nul') do (
        echo Removing image: %%i
        podman rmi -f %%i >nul 2>&1
    )

    :: Prune unused images
    echo Pruning unused images...
    podman image prune -f >nul 2>&1
) else (
    echo Podman not found. Skipping image cleanup.
)

:: Step 5: Remove all data and configuration files
echo Step 5: Removing all data and configuration files...
if exist "%INSTALL_DIR%\data" (
    echo Removing data directory...
    rmdir /s /q "%INSTALL_DIR%\data"
)

:: Check for other data directories
echo Checking for other data directories...
if exist "%LOCALAPPDATA%\EcoEdge" (
    echo Removing %LOCALAPPDATA%\EcoEdge
    rmdir /s /q "%LOCALAPPDATA%\EcoEdge"
)

if exist "%APPDATA%\EcoEdge" (
    echo Removing %APPDATA%\EcoEdge
    rmdir /s /q "%APPDATA%\EcoEdge"
)

if exist "%PROGRAMDATA%\EcoEdge" (
    echo Removing %PROGRAMDATA%\EcoEdge
    rmdir /s /q "%PROGRAMDATA%\EcoEdge"
)

:: Step 6: Remove all registry entries and startup configurations
echo Step 6: Removing all registry entries and startup configurations...

:: Remove from Run registry key
echo Removing from Run registry key...
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "EcoEdgeMonitor" /f >nul 2>&1

:: Remove scheduled tasks
echo Removing scheduled tasks...
schtasks /query /tn "EcoEdgeMonitor" >nul 2>&1
if %errorLevel% equ 0 (
    echo Removing EcoEdgeMonitor scheduled task...
    schtasks /delete /tn "EcoEdgeMonitor" /f >nul 2>&1
)

:: Remove registry entries
echo Removing registry entries...
reg delete "HKCU\Software\EcoEdge" /f >nul 2>&1
reg delete "HKCU\Software\Eco Edge" /f >nul 2>&1
reg delete "HKLM\Software\EcoEdge" /f >nul 2>&1
reg delete "HKLM\Software\Eco Edge" /f >nul 2>&1
reg delete "HKCU\Software\Classes\eco-edge" /f >nul 2>&1
reg delete "HKLM\Software\Classes\eco-edge" /f >nul 2>&1

echo.
echo Force uninstallation completed.

:End
echo.
echo Press any key to exit...
pause >nul
