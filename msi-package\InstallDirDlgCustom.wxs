<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
   <Fragment>
      <UI>
         <Dialog Id="InstallDirDlgReadOnly" Width="370" Height="270" Title="ESXP Edge Agent Setup">
            <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="Next" />
            <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="Back" />
            <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="Cancel">
               <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
            </Control>

            <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes" Text="Click Next to install to the default folder." />
            <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes" Text="{\WixUI_Font_Title}Installation Folder" />
            <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="44" TabSkip="no" Text="WixUI_Bmp_Banner" />
            <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="370" Height="0" />
            <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="370" Height="0" />

            <Control Id="FolderLabel" Type="Text" X="20" Y="60" Width="290" Height="30" NoPrefix="yes" Text="The application will be installed to the following location (this cannot be changed):" />

            <!-- Read-only text box for installation directory -->
            <Control Id="Folder" Type="Text" X="20" Y="100" Width="320" Height="18" Text="[LocalAppDataFolder]EcoEdgeAgent" />

            <!-- Hidden Change button (not visible but needed for WiX UI sequence) -->
            <Control Id="ChangeFolder" Type="PushButton" X="20" Y="120" Width="0" Height="0" Text="Change" />
         </Dialog>
      </UI>
   </Fragment>
</Wix>
