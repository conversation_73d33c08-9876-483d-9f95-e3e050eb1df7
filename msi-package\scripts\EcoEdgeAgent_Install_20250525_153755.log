**********************
PowerShell transcript start
Start time: 20250525153755
Username: APA\SESA808825
RunAs User: APA\SESA808825
Configuration Name: 
Machine: WIN01502063L (Microsoft Windows NT 10.0.22631.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -noexit -command try { . "c:\Users\<USER>\AppData\Local\Programs\Windsurf\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch {}
Process ID: 42304
PSVersion: 7.5.0
PSEdition: Core
GitCommitId: 7.5.0
OS: Microsoft Windows 10.0.22631
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_153755.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_153755.log
[2025-05-25 15:37:55] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 15:37:55] [INFO] Checking for Podman installation...
[2025-05-25 15:37:55] [WARNING] Podman not found. Installing Podman...
[2025-05-25 15:37:55] [INFO] Downloading Podman installer from https://github.com/containers/podman/releases/download/v5.5.0/podman-5.5.0-setup.exe ...
[2025-05-25 15:37:55] [INFO] Installer will be saved to: C:\Users\<USER>\AppData\Local\Temp\podman-setup.exe
[2025-05-25 15:38:01] [INFO] Download completed. File size: 29828432 bytes
[2025-05-25 15:38:01] [INFO] Running Podman installer...
[2025-05-25 15:38:09] [INFO] Installer process exit code: 0
[2025-05-25 15:38:09] [INFO] Podman installer completed successfully.
[2025-05-25 15:38:09] [INFO] Podman installation completed.
[2025-05-25 15:38:09] [INFO] Refreshing PATH environment variable...
[2025-05-25 15:38:09] [INFO] PATH refreshed successfully.
[2025-05-25 15:38:09] [INFO] Current PATH: C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\Windows Imaging\;C:\Program Files\CMake\bin;C:\Program Files\Java\jre1.8.0_441\bin;C:\Program Files\Graphviz\bin;C:\Program Files\PowerShell\7\;C:\Program Files\dotnet\;C:\Program Files\Go\bin;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\1E\Client\Extensibility\NomadBranch;C:\Program Files\WiX Toolset v5.0\bin\;C:\Program Files\RedHat\Podman\
[2025-05-25 15:38:09] [INFO] Verifying Podman installation...
Cannot connect to Podman. Please verify your connection to the Linux system using `podman system connection list`, or try `podman machine init` and `podman machine start` to manage a new Linux VM
Error: unable to connect to Podman socket: failed to connect: dial tcp 127.0.0.1:56902: connectex: No connection could be made because the target machine actively refused it.
[2025-05-25 15:38:10] [INFO] Podman installed successfully. Version:
[2025-05-25 15:38:10] [INFO] Checking for existing Podman machine...
[2025-05-25 15:38:11] [INFO] Podman machine already exists. Checking if it's running...
[2025-05-25 15:38:11] [INFO] Starting existing Podman machine...
[2025-05-25 15:38:21] [INFO] Machine start output: Starting machine "podman-machine-default" your 131072x1 screen size is bogus. expect trouble API forwarding listening on: npipe:////./pipe/docker_engine  Docker API clients default to this address. You do not need to set DOCKER_HOST. Machine "podman-machine-default" started successfully
[2025-05-25 15:38:21] [INFO] Testing Podman connection...
[2025-05-25 15:38:22] [INFO] Podman connection test completed successfully
WARNING: No config file found! Using default values.
[2025-05-25 15:38:22] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 15:38:22] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 107691520 bytes)
[2025-05-25 15:38:22] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 15:38:24] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 15:38:24] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52726784 bytes)
[2025-05-25 15:38:24] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 15:38:25] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
Created data directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\data
[2025-05-25 15:38:26] [INFO] Checking if eco-edge-network exists...
[2025-05-25 15:38:26] [INFO] eco-edge-network already exists
[2025-05-25 15:38:26] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER a62bbe9be120  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 15:38:26] [INFO] Checking for existing containers...
[2025-05-25 15:38:27] [INFO] Found existing container: octopus-service
[2025-05-25 15:38:27] [INFO] Removing container: octopus-service
[2025-05-25 15:38:27] [INFO] Remove container output: octopus-service
[2025-05-25 15:38:28] [INFO] Found existing container: ewp-edge
[2025-05-25 15:38:28] [INFO] Removing container: ewp-edge
[2025-05-25 15:38:29] [INFO] Remove container output: ewp-edge
[2025-05-25 15:38:29] [INFO] Running octopus-service container...
[2025-05-25 15:38:29] [INFO] Added backend environment arguments: -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 15:38:29] [INFO] Added volume arguments: -v C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\data:/app/data
[2025-05-25 15:38:29] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -v C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\data:/app/data --user 1000:1000 octopus-service:latest
[2025-05-25 15:38:29] [INFO] Container start output: cdfa7995d931c836fa4382296838091daa840274e6499b44ab264102350542ec
[2025-05-25 15:38:32] [INFO] octopus-service container started successfully
[2025-05-25 15:38:32] [INFO] Running ewp-edge container...
[2025-05-25 15:38:32] [INFO] Added frontend environment arguments: -e NGINX_PORT=80 -e API_URL=http://localhost:9080
[2025-05-25 15:38:32] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -e NGINX_PORT=80 -e API_URL=http://localhost:9080 ewp-edge:latest
[2025-05-25 15:38:32] [INFO] Container start output: bf2a0aa92f0cf25ef0e317d4d31ad80cc2743e37119c84d5a4587f174c30176f
[2025-05-25 15:38:35] [INFO] ewp-edge container started successfully
[2025-05-25 15:38:35] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 15:38:35] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 15:38:35] [INFO] Created directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 15:38:35] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 15:38:35] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 15:38:35] [INFO] Found 30 files in source directory
[2025-05-25 15:38:35] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 15:38:35] [INFO] Files copied successfully
[2025-05-25 15:38:35] [INFO] Setting up startup shortcut...
[2025-05-25 15:38:35] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 15:38:35] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 15:38:35] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 15:38:35] [INFO] Monitor executable found, creating shortcut
[2025-05-25 15:38:35] [INFO] Startup shortcut created successfully
[2025-05-25 15:38:35] [INFO] No existing Eco Edge Monitor processes found
[2025-05-25 15:38:35] [INFO] Starting Eco Edge Monitor application
[2025-05-25 15:38:35] [INFO] Started Eco Edge Monitor with process ID: 36424
[2025-05-25 15:38:35] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 15:38:37] [INFO] Verified Eco Edge Monitor is running with process ID: 36424
[2025-05-25 15:38:37] [INFO] Eco Edge Agent installation complete.
**********************
PowerShell transcript end
End time: 20250525153837
**********************
