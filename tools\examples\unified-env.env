# Unified configuration file for Eco Edge
# This file contains all configuration settings for the Eco Edge application
# It supports both environment variables and volume mounts
# The format is based on .env files with specific prefixes for each section

# Backend environment variables
BACKEND_ENV_REST_PORT=9080
BACKEND_ENV_REST_HOST=0.0.0.0
BACKEND_ENV_CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
BACKEND_ENV_CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem
BACKEND_ENV_CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem
BACKEND_ENV_CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem
BACKEND_ENV_CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt
BACKEND_ENV_CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url
BACKEND_ENV_DB_PATH=/app/db
BACKEND_ENV_AUTH_DB_PATH=/app/db/auth/auth.db
BACKEND_ENV_CONFIG_DB_PATH=/app/db/auth/config.db

# Frontend environment variables
FRONTEND_ENV_API_URL=http://octopus-service:9080
FRONTEND_ENV_NGINX_PORT=80

# Backend volume mounts
BACKEND_VOL_/app/logs=C:\data\backend\logs
BACKEND_VOL_/app/certs=C:\data\backend\certs
BACKEND_VOL_/app/db=C:\data\backend\db
