# install_simple.ps1
# Simplified installation script for Eco Edge Agent

param(
    [string]$ConfigFilePath
)

# Create a log file to track execution
$logFile = Join-Path -Path $env:TEMP -ChildPath "eco_edge_install_simple_log.txt"

# Function to log messages
function Write-Log {
    param (
        [string]$Message
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "$timestamp - $Message" | Out-File -FilePath $logFile -Append
    Write-Host "$timestamp - $Message"
}

# Log the start of the script
Write-Log "Starting simplified installation script"
Write-Log "Script is running from: $PSScriptRoot"

# Log environment variables and parameters
Write-Log "ConfigFilePath: $ConfigFilePath"
Write-Log "INSTALLDIR: $env:INSTALLDIR"
Write-Log "Current directory: $(Get-Location)"
Write-Log "Current user: $env:USERNAME"
Write-Log "Is admin: $([bool](([System.Security.Principal.WindowsIdentity]::GetCurrent()).groups -match 'S-1-5-32-544'))"

# Determine the installation root directory (parent of scripts folder)
$installRoot = Split-Path -Parent $PSScriptRoot
Write-Log "Installation root directory: $installRoot"

# Step 1: Check for docker_tars directory
$dockerTarsDir = Join-Path $installRoot "docker_tars"
Write-Log "Checking for docker_tars directory: $dockerTarsDir"
if (Test-Path $dockerTarsDir) {
    Write-Log "docker_tars directory found"
    $tarFiles = Get-ChildItem -Path $dockerTarsDir -Filter "*.tar"
    Write-Log "Found $($tarFiles.Count) tar files in docker_tars directory"
    foreach ($file in $tarFiles) {
        Write-Log "  - $($file.Name)"
    }
} else {
    Write-Log "docker_tars directory not found"
}

# Step 2: Check for eco-edge directory
$ecoEdgeDir = Join-Path $installRoot "eco-edge"
Write-Log "Checking for eco-edge directory: $ecoEdgeDir"
if (Test-Path $ecoEdgeDir) {
    Write-Log "eco-edge directory found"
    $exeFiles = Get-ChildItem -Path $ecoEdgeDir -Filter "*.exe" -Recurse
    Write-Log "Found $($exeFiles.Count) exe files in eco-edge directory"
    foreach ($file in $exeFiles) {
        Write-Log "  - $($file.FullName.Replace($ecoEdgeDir, ''))"
    }
} else {
    Write-Log "eco-edge directory not found"
}

# Step 3: Create a test file in the installation directory
try {
    # Use the installation root we already determined
    Write-Log "Using install directory: $installRoot"

    # Verify installation location
    $programDataPath = [Environment]::GetFolderPath("CommonApplicationData")
    $localAppDataPath = [Environment]::GetFolderPath("LocalApplicationData")
    Write-Log "ProgramData path: $programDataPath"
    Write-Log "LocalAppData path: $localAppDataPath"

    if ($installRoot.StartsWith($programDataPath)) {
        Write-Log "Installation is in ProgramData folder"
    } elseif ($installRoot.StartsWith($localAppDataPath)) {
        Write-Log "Installation is in LocalAppData folder (per-user installation)"
    } else {
        Write-Log "Installation is in custom location: $installRoot"
    }

    # Try to create a test file, but don't fail if we can't
    try {
        $testFile = Join-Path -Path $installRoot -ChildPath "install_simple_test.txt"
        "Installation test successful at $(Get-Date)" | Out-File -FilePath $testFile -ErrorAction Stop
        Write-Log "Created test file at: $testFile"

        # Test file permissions if we were able to create the file
        try {
            $acl = Get-Acl -Path $testFile -ErrorAction SilentlyContinue
            if ($acl) {
                Write-Log "File permissions: $($acl.AccessToString)"
            }
        } catch {
            Write-Log "Could not read file permissions: $_"
        }
    } catch {
        Write-Log "Could not create test file in installation directory: $_"
        Write-Log "Creating test file in user's temp directory instead"

        $testFile = Join-Path -Path $env:TEMP -ChildPath "eco_edge_install_simple_test.txt"
        "Installation test successful at $(Get-Date)" | Out-File -FilePath $testFile
        Write-Log "Created test file at: $testFile (in temp folder)"
    }
} catch {
    Write-Log "Error during installation test: $_"
    # Don't exit with error, just log and continue
    Write-Log "Continuing despite test error"
}

# Log successful completion
Write-Log "Simplified installation script completed successfully"
exit 0
