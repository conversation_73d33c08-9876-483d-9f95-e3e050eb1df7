# Chocolatey package files
*.nupkg

# Podman image files
*.tar

# Don't ignore Chocolatey installation scripts
!tools/chocolateyinstall/*.ps1

# User-specific files
*.user
*.suo
*.userprefs
*.sln.docstates

# Build results
[Dd]ebug/
[Rr]elease/
x64/
!msi-package/CustomAction/x64/**
[Bb]in/
[Oo]bj/

# Exclude Flutter build directory but keep the source code
eco_edge_monitor/build/

# Visual Studio files
.vs/
*.vscode/
*.suo
*.user
*.userosscache
*.sln.docstates

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
eco_edge_monitor/windows/flutter/generated_plugin_registrant.cc
eco_edge_monitor/windows/flutter/generated_plugin_registrant.h
eco_edge_monitor/windows/flutter/generated_plugins.cmake
tools/chocolateyinstall/eco-edge-monitor/eco_edge_monitor.exe
tools/chocolateyinstall/eco-edge-monitor/screen_retriever_plugin.dll
tools/chocolateyinstall/eco-edge-monitor/system_tray_plugin.dll
tools/chocolateyinstall/eco-edge-monitor/tray_manager_plugin.dll
tools/chocolateyinstall/eco-edge-monitor/url_launcher_windows_plugin.dll
tools/chocolateyinstall/eco-edge-monitor/window_manager_plugin.dll
tools/chocolateyinstall/eco-edge-monitor/data/app.so
msi-package/eco-edge/data/app.so
msi-package/eco-edge/data/icudtl.dat
msi-package/eco-edge/data/flutter_assets/AssetManifest.bin
msi-package/eco-edge/data/flutter_assets/AssetManifest.json
msi-package/eco-edge/data/flutter_assets/FontManifest.json
msi-package/eco-edge/data/flutter_assets/NativeAssetsManifest.json
msi-package/eco-edge/data/flutter_assets/NOTICES.Z
msi-package/eco-edge/data/flutter_assets/assets/icons/app_icon_running.png
msi-package/eco-edge/data/flutter_assets/assets/icons/app_icon_running.svg
msi-package/eco-edge/data/flutter_assets/assets/icons/app_icon_stopped.png
msi-package/eco-edge/data/flutter_assets/assets/icons/app_icon_stopped.svg
msi-package/eco-edge/data/flutter_assets/assets/icons/app_icon_warning.png
msi-package/eco-edge/data/flutter_assets/assets/icons/app_icon_warning.svg
msi-package/eco-edge/data/flutter_assets/assets/icons/app_icon.png
msi-package/eco-edge/data/flutter_assets/assets/icons/app_icon.svg
msi-package/eco-edge/data/flutter_assets/fonts/MaterialIcons-Regular.otf
msi-package/eco-edge/data/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf
msi-package/eco-edge/data/flutter_assets/packages/window_manager/images/ic_chrome_close.png
msi-package/eco-edge/data/flutter_assets/packages/window_manager/images/ic_chrome_maximize.png
msi-package/eco-edge/data/flutter_assets/packages/window_manager/images/ic_chrome_minimize.png
msi-package/eco-edge/data/flutter_assets/packages/window_manager/images/ic_chrome_unmaximize.png
msi-package/eco-edge/data/flutter_assets/shaders/ink_sparkle.frag
msi-package/eco-edge/eco_edge_monitor.exe
msi-package/eco-edge/flutter_windows.dll
msi-package/eco-edge/screen_retriever_plugin.dll
msi-package/eco-edge/system_tray_plugin.dll
msi-package/eco-edge/tray_manager_plugin.dll
msi-package/eco-edge/url_launcher_windows_plugin.dll
msi-package/eco-edge/window_manager_plugin.dll
msi-package/output/ESXPEdgeAgent.msi
msi-package/output/ESXPEdgeAgent.wixpdb
msi-package/output/FileBrowseDlg.wixobj
msi-package/output/FlutterAssets.wixobj
msi-package/output/FlutterAssets.wxs
msi-package/output/InstallDirDlgCustom.wixobj
msi-package/output/light_verbose.log
msi-package/output/Product.wixobj
msi-package/output/WixUI_Custom.wixobj
