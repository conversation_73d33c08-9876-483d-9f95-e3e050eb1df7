<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
   <Fragment>
      <UI Id="WixUI_Custom">
         <TextStyle Id="WixUI_Font_Normal" FaceName="Tahoma" Size="8" />
         <TextStyle Id="WixUI_Font_Bigger" FaceName="Tahoma" Size="12" />
         <TextStyle Id="WixUI_Font_Title" FaceName="Tahoma" Size="9" Bold="yes" />

         <Property Id="DefaultUIFont" Value="WixUI_Font_Normal" />
         <Property Id="WixUI_Mode" Value="InstallDir" />

         <!-- Standard dialogs -->
         <DialogRef Id="BrowseDlg" />
         <DialogRef Id="DiskCostDlg" />
         <DialogRef Id="ErrorDlg" />
         <DialogRef Id="FatalError" />
         <DialogRef Id="FilesInUse" />
         <DialogRef Id="MsiRMFilesInUse" />
         <DialogRef Id="PrepareDlg" />
         <DialogRef Id="ProgressDlg" />
         <DialogRef Id="ResumeDlg" />
         <DialogRef Id="UserExit" />
         <DialogRef Id="WelcomeDlg" />
         <DialogRef Id="LicenseAgreementDlg" />
         <!-- Use our custom InstallDirDlg instead of the standard one -->
         <!-- <DialogRef Id="InstallDirDlg" /> -->
         <DialogRef Id="VerifyReadyDlg" />
         <DialogRef Id="MaintenanceWelcomeDlg" />
         <DialogRef Id="MaintenanceTypeDlg" />

         <!-- Custom dialogs -->
         <DialogRef Id="FileBrowseDlg" />

         <!-- UI sequence -->
         <!-- Original license flow (commented out for now) -->
         <!-- <Publish Dialog="WelcomeDlg" Control="Next" Event="NewDialog" Value="LicenseAgreementDlg">NOT Installed</Publish> -->
         <!-- Skip license agreement for now -->
         <Publish Dialog="WelcomeDlg" Control="Next" Event="NewDialog" Value="InstallDirDlgReadOnly">NOT Installed</Publish>
         <Publish Dialog="WelcomeDlg" Control="Next" Event="NewDialog" Value="VerifyReadyDlg">Installed AND PATCH</Publish>

         <!-- License dialog references (kept for future use) -->
         <Publish Dialog="LicenseAgreementDlg" Control="Back" Event="NewDialog" Value="WelcomeDlg">1</Publish>
         <Publish Dialog="LicenseAgreementDlg" Control="Next" Event="NewDialog" Value="InstallDirDlgReadOnly">LicenseAccepted = "1"</Publish>

         <!-- Original back navigation (commented out for now) -->
         <!-- <Publish Dialog="InstallDirDlgReadOnly" Control="Back" Event="NewDialog" Value="LicenseAgreementDlg">1</Publish> -->
         <!-- Modified back navigation to skip license screen -->
         <Publish Dialog="InstallDirDlgReadOnly" Control="Back" Event="NewDialog" Value="WelcomeDlg">1</Publish>
         <!-- Skip validation since we're using a fixed path -->
         <Publish Dialog="InstallDirDlgReadOnly" Control="Next" Event="NewDialog" Value="FileBrowseDlg">1</Publish>

         <Publish Dialog="FileBrowseDlg" Control="Back" Event="NewDialog" Value="InstallDirDlgReadOnly">1</Publish>
         <Publish Dialog="FileBrowseDlg" Control="Next" Event="NewDialog" Value="VerifyReadyDlg">1</Publish>

         <Publish Dialog="VerifyReadyDlg" Control="Back" Event="NewDialog" Value="FileBrowseDlg" Order="1">NOT Installed</Publish>
         <Publish Dialog="VerifyReadyDlg" Control="Back" Event="NewDialog" Value="MaintenanceTypeDlg" Order="2">Installed AND NOT PATCH</Publish>
         <Publish Dialog="VerifyReadyDlg" Control="Back" Event="NewDialog" Value="WelcomeDlg" Order="3">Installed AND PATCH</Publish>

         <Publish Dialog="MaintenanceWelcomeDlg" Control="Next" Event="NewDialog" Value="MaintenanceTypeDlg">1</Publish>

         <Publish Dialog="MaintenanceTypeDlg" Control="RepairButton" Event="NewDialog" Value="VerifyReadyDlg">1</Publish>
         <Publish Dialog="MaintenanceTypeDlg" Control="RemoveButton" Event="NewDialog" Value="VerifyReadyDlg">1</Publish>
         <Publish Dialog="MaintenanceTypeDlg" Control="Back" Event="NewDialog" Value="MaintenanceWelcomeDlg">1</Publish>
      </UI>

      <UIRef Id="WixUI_Common" />
   </Fragment>
</Wix>
