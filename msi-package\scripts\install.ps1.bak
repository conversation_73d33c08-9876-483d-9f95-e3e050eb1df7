# PowerShell installation script for Eco Edge Agent (Windows)
# 1. Check if <PERSON><PERSON> is installed; if not, install it
# 2. Prompt for config file (installer <PERSON><PERSON> will pass path as argument)
# 3. Load docker images from docker_tars (octopus-service.tar, ewp-edge.tar) using podman
# 4. Run backend and frontend containers with -e arguments and -v volumns arguments.
# if config.json file is provided use values from config.json file
# otherwise use default values
# 5. Copy/install eco-edge application files

param(
    [string]$ConfigFilePath,
    [switch]$EnableLogging = $true,
    [string]$LogPath = "",
    [int]$backendPort = 9080,
    [int]$frontendPort = 8080
)

# Setup logging
if ($EnableLogging) {
    if ([string]::IsNullOrEmpty($LogPath)) {
        # Use current directory instead of TEMP
        $currentDir = $PSScriptRoot
        $timestamp = Get-Date -Format 'yyyyMMdd_HHmmss'
        $logFileName = "EcoEdgeAgent_Install_$timestamp.log"
        $LogPath = Join-Path $currentDir $logFileName
    }
    
    # Start transcript to capture all output
    Start-Transcript -Path $LogPath -Append
    Write-Host "Logging enabled. Log file: $LogPath"
}

# Helper function for logging
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # Always write to console
    if ($Level -eq "ERROR") {
        Write-Host $logMessage -ForegroundColor Red
    }
    elseif ($Level -eq "WARNING") {
        Write-Host $logMessage -ForegroundColor Yellow
    }
    else {
        Write-Host $logMessage
    }
}

Write-Log "Starting Eco Edge Agent installation..."

# Function to check if a port is in use
function Test-PortInUse {
    param(
        [int]$Port
    )
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $result = $tcpClient.ConnectAsync('127.0.0.1', $Port).Wait(100)
        $tcpClient.Close()
        return $result
    }
    catch {
        return $false
    }
}

# Function to get process using a port
function Get-ProcessUsingPort {
    param(
        [int]$Port
    )
    
    try {
        # Using netstat to find processes using specific ports
        $netstatOutput = & netstat -ano | Select-String -Pattern ":$Port\s" | Select-String -Pattern "LISTENING"
        
        if ($netstatOutput) {
            # Extract PID from the netstat output
            $pidMatch = $netstatOutput -match '\s+(\d+)$'
            if ($pidMatch) {
                $processPid = $matches[1]
                
                # Get process name from PID
                $process = Get-Process -Id $processPid -ErrorAction SilentlyContinue
                if ($process) {
                    return @{
                        "PID" = $processPid
                        "ProcessName" = $process.ProcessName
                        "Path" = $process.Path
                    }
                }
                return @{
                    "PID" = $processPid
                    "ProcessName" = "Unknown"
                    "Path" = "Unknown"
                }
            }
        }
        return $null
    }
    catch {
        Write-Log "Error checking process using port $Port: $($_.Exception.Message)" "WARNING"
        return $null
    }
}

# Step 1: Check Podman installation
Write-Log "Checking for Podman installation..."
$podmanInstalled = Get-Command podman -ErrorAction SilentlyContinue

# Check if either port is in use by a non-Podman process and exit if so
if (Test-PortInUse -Port $frontendPort) {
    $frontendProcess = Get-ProcessUsingPort -Port $frontendPort
    $errorMsg = "Frontend port $frontendPort is in use by process: $($frontendProcess.ProcessName) (PID: $($frontendProcess.PID)). Installation cannot proceed."
    Write-Log $errorMsg "ERROR"
    throw $errorMsg
}

if (Test-PortInUse -Port $backendPort) {
    $backendProcess = Get-ProcessUsingPort -Port $backendPort
    $errorMsg = "Backend port $backendPort is in use by process: $($backendProcess.ProcessName) (PID: $($backendProcess.PID)). Installation cannot proceed."
    Write-Log $errorMsg "ERROR"
    throw $errorMsg
}

Write-Log "Ports are available: Frontend=$frontendPort, Backend=$backendPort"

# Continue with the rest of the installation...
Write-Log "Installation completed successfully."
