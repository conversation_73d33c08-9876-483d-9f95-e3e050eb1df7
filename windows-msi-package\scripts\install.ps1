# PowerShell installation script for Eco Edge Agent (Windows)
# 1. Check if <PERSON><PERSON> is installed; if not, install it
# 2. Prompt for config file (installer <PERSON><PERSON> will pass path as argument)
# 3. Load docker images from docker_tars (octopus-service.tar, ewp-edge.tar) using podman
# 4. Run backend and frontend containers with -e arguments and -v volumns arguments.
# if config.json file is provided use values from config.json file
# otherwise use default values
# 5. Copy/install eco-edge application files

param(
    [string]$ConfigFilePath
)

Write-Host "Starting Eco Edge Agent installation..."

# Step 1: Check Podman installation
$podmanInstalled = Get-Command podman -ErrorAction SilentlyContinue
if (-not $podmanInstalled) {
    Write-Host "Podman not found. Installing Podman..."
    $podmanMsiUrl = "https://github.com/containers/podman/releases/latest/download/podman-setup.exe"
    $installerPath = Join-Path $env:TEMP "podman-setup.exe"
    try {
        Write-Host "Downloading Podman installer from $podmanMsiUrl ..."
        Invoke-WebRequest -Uri $podmanMsiUrl -OutFile $installerPath -UseBasicParsing
        Write-Host "Running Podman installer..."
        Start-Process -FilePath $installerPath -ArgumentList "/install /quiet /norestart" -Wait -NoNewWindow
        Remove-Item $installerPath -Force
        Write-Host "Podman installation completed."
    }
    catch {
        Write-Error "Failed to download or install Podman: $_"
        exit 1
    }
    # Refresh PATH for current session
    $env:Path = [System.Environment]::GetEnvironmentVariable('Path', [System.EnvironmentVariableTarget]::Machine)
    # Verify installation
    $podmanInstalled = Get-Command podman -ErrorAction SilentlyContinue
    if (-not $podmanInstalled) {
        Write-Error "Podman installation failed or podman not found in PATH."
        exit 1
    }
    else {
        Write-Host "Podman installed successfully."
    }
    # Placeholder: Podman registry login (if needed)
    # Example: podman login <registry> --username <user> --password <pass>
    # TODO: Implement registry login if required

}
else {
    Write-Host "Podman is already installed."
}

# Step 2: Prompt for config file (already passed as $ConfigFilePath)
if ($ConfigFilePath) {
    Write-Host "Config file provided: $ConfigFilePath"
}
else {
    Write-Warning "No config file path provided! Using default values."
}

# Step 3: Load Docker images from .tar files
$dockerTarsDir = Join-Path $PSScriptRoot '../docker_tars'
$images = @('octopus-service.tar', 'ewp.tar')
foreach ($img in $images) {
    $imgPath = Join-Path $dockerTarsDir $img
    if (Test-Path $imgPath) {
        Write-Host "Loading image $imgPath into Podman..."
        podman load -i $imgPath
    }
    else {
        Write-Error "$imgPath not found! Installation cannot continue without required images."
        exit 1
    }
}

# Step 4: Run backend and frontend containers with -e and -v arguments
# Define default env and volume maps
$backendEnvVarsMap = @{
    "REST_PORT"                         = "9080"
    "REST_HOST"                         = "0.0.0.0"
    "CLOUD_DEVICE_CERTIFICATE_PATH"     = "/app/certs/device_cert.pem"
    "CLOUD_DEVICE_PRIVATE_KEY_PATH"     = "/app/certs/device_key.pem"
    "CLOUD_CONNECTION_CERT_PATH"        = "/app/certs/connectionCertificate.pem"
    "CLOUD_CONNECTION_PRIVATE_KEY_PATH" = "/app/certs/privateKey.pem"
    "CLOUD_CONNECTION_STRING_PATH"      = "/app/certs/connectionString.txt"
    "CLOUD_BOOTSTRAP_SERVER_URL"        = "boot-strap-url"  # TODO: Change this value from CICD
    "DB_PATH"                           = "/app/db"
    "AUTH_DB_PATH"                      = "/app/db/auth/auth.db"
    "AUTH_CONFIG_DB_PATH"               = "/app/db/auth/config.db"
    "SNMP_CONFIG_DB_PATH"               = "/app/db/snmp/config.db"
}

$frontendEnvVarsMap = @{
    "API_URL"    = "http://localhost:9080"
    "NGINX_PORT" = "80"
}

# Default volume mounts (only for backend)
$backendVolumesMap = @{
    # Format: "host_path" = "container_path"
    # Example: "C:\\data\\backend" = "/app/data"
}

# Helper: Merge config env/vols into defaults
function Merge-EnvVars {
    param(
        [hashtable]$defaultMap,
        [hashtable]$overrideMap
    )
    $merged = @{}
    foreach ($k in $defaultMap.Keys) {
        $merged[$k] = $defaultMap[$k]
    }
    if ($overrideMap) {
        foreach ($k in $overrideMap.Keys) {
            $merged[$k] = $overrideMap[$k]
        }
    }
    return $merged
}

function Merge-Volumes {
    param(
        [hashtable]$defaultMap,
        [array]$overrideList
    )
    $merged = @{}
    foreach ($k in $defaultMap.Keys) {
        $merged[$k] = $defaultMap[$k]
    }
    if ($overrideList) {
        foreach ($vol in $overrideList) {
            # Parse host_path:container_path
            if ($vol -match "^(.+?):(.+)$") {
                $hostPath = $Matches[1]
                $container = $Matches[2]
                $merged[$hostPath] = $container
            }
        }
    }
    return $merged
}

$backendEnv = $backendEnvVarsMap
$frontendEnv = $frontendEnvVarsMap
$backendVolumes = $backendVolumesMap

if ($ConfigFilePath -and (Test-Path $ConfigFilePath)) {
    try {
        $config = Get-Content $ConfigFilePath | ConvertFrom-Json
        if ($config.backendEnv) {
            $backendEnv = Merge-EnvVars $backendEnvVarsMap $config.backendEnv
        }
        if ($config.frontendEnv) {
            $frontendEnv = Merge-EnvVars $frontendEnvVarsMap $config.frontendEnv
        }
        if ($config.backendVol) {
            $backendVolumes = Merge-Volumes $backendVolumesMap $config.backendVol
        }
    }
    catch {
        Write-Warning "Failed to parse config file. Using default values."
    }
}

# Build env and volume args for backend and frontend
$backendEnvArgs = @()
foreach ($key in $backendEnv.Keys) {
    $backendEnvArgs += "-e $key=$($backendEnv[$key])"
}

$frontendEnvArgs = @()
foreach ($key in $frontendEnv.Keys) {
    $frontendEnvArgs += "-e $key=$($frontendEnv[$key])"
}
$volArgs = @()
foreach ($hostPath in $backendVolumes.Keys) {
    $volArgs += "-v ${hostPath}:$($backendVolumes[$hostPath])"
}


# Check if eco-edge-network exists, create if not
$networkExists = podman network ls --format "{{.Name}}" | Select-String -Pattern "^eco-edge-network$" -Quiet
if (-not $networkExists) {
    Write-Host "Creating eco-edge-network..."
    podman network create eco-edge-network
}

# Check and remove existing containers
Write-Host "Checking for existing containers..."
$containers = @("octopus-service", "ewp-edge")

foreach ($container in $containers) {
    # Check if container exists
    $containerExists = podman ps -a --format "{{.Names}}" | Select-String -Pattern "^$container$" -Quiet
    
    if ($containerExists) {
        Write-Host "Found existing container: $container"
        
        # Check if container is running
        $containerRunning = podman ps --format "{{.Names}}" | Select-String -Pattern "^$container$" -Quiet
        
        if ($containerRunning) {
            Write-Host "Stopping container: $container"
            podman stop $container
        }
        
        Write-Host "Removing container: $container"
        podman rm $container
    }
}

# Run octopus-service container
Write-Host "Running octopus-service container..."
$podmanRunArgs = @("-d", "--name", "octopus-service", "--restart", "always", "--network", "eco-edge-network", "--network-alias", "octopus-service", "--hostname", "octopus-service")

# Add environment arguments
if ($backendEnvArgs.Count -gt 0) {
    $podmanRunArgs += $backendEnvArgs
}

# Add volume arguments only if there are any
if ($volArgs.Count -gt 0) {
    $podmanRunArgs += $volArgs
}

$podmanRunArgs += @("--user", "1000:1000", "octopus-service:latest")

# Convert array to command string and execute
$podmanRunCommand = "podman run " + ($podmanRunArgs -join " ")
Invoke-Expression $podmanRunCommand

# Run ewp-edge container
Write-Host "Running ewp-edge container..."
$podmanRunArgs = @("-d", "--name", "ewp-edge", "--restart", "always", "--network", "eco-edge-network", "--network-alias", "ewp-edge", "--hostname", "ewp-edge")

# Add environment arguments
if ($frontendEnvArgs.Count -gt 0) {
    $podmanRunArgs += $frontendEnvArgs
}

$podmanRunArgs += @("ewp-edge:latest")

# Convert array to command string and execute
$podmanRunCommand = "podman run " + ($podmanRunArgs -join " ")
Invoke-Expression $podmanRunCommand

# Step 5: Install Eco Edge Monitor system tray application
Write-Host "Installing Eco Edge Monitor system tray application..."

# Use ProgramData folder which doesn't require admin privileges
$installDir = Join-Path $env:ProgramData "EcoEdgeAgent"
$monitorDir = Join-Path $installDir "monitor"

# Create directory with proper error handling
if (-not (Test-Path $monitorDir)) {
    try {
        New-Item -ItemType Directory -Path $monitorDir -Force -ErrorAction Stop | Out-Null
        Write-Host "Created directory: $monitorDir"
    }
    catch {
        Write-Error "Failed to create directory '$monitorDir': $_"
        Write-Warning "Make sure you're running with administrator privileges."
    }
}

# Copy system tray application files
$monitorSourceDir = Join-Path $PSScriptRoot "../eco-edge"
if (Test-Path $monitorSourceDir) {
    Copy-Item -Path "$monitorSourceDir\*" -Destination $monitorDir -Recurse -Force
}
else {
    Write-Warning "System tray application files not found. The system tray application will not be installed."
}

# TEMPORARILY COMMENTED OUT: Create startup shortcut for system tray application
# This code is commented out to avoid potential antivirus detection issues
<#
$startupPath = [Environment]::GetFolderPath("Startup")
$monitorExePath = Join-Path $monitorDir "eco_edge_monitor.exe"
$startupShortcutFile = Join-Path $startupPath "Eco Edge Monitor.lnk"

if (Test-Path $monitorExePath) {
    try {
        $WshShell = New-Object -comObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut($startupShortcutFile)
        $Shortcut.TargetPath = $monitorExePath
        $Shortcut.Description = "Eco Edge Monitor"
        $Shortcut.WorkingDirectory = $monitorDir
        $Shortcut.Save()
    }
    catch {
        Write-Warning "Could not create startup shortcut: $_"
    }
}
#>

# Check for monitor executable path
$monitorExePath = Join-Path $monitorDir "eco_edge_monitor.exe"
if (Test-Path $monitorExePath) {
    # Kill any existing instances of the application
    try {
        $processes = Get-Process -Name "eco_edge_monitor" -ErrorAction SilentlyContinue
        if ($processes) {
            $processes | ForEach-Object { $_.Kill() }
            Write-Host "Stopped existing Eco Edge Monitor instances."
            Start-Sleep -Seconds 1 # Give it time to fully terminate
        }
    }
    catch {
        Write-Warning "Could not stop existing Eco Edge Monitor processes: $_"
    }

    # Start the system tray application
    Start-Process -FilePath $monitorExePath -WorkingDirectory $monitorDir
    Write-Host "Eco Edge Monitor system tray application has been installed and started."

    # Wait a moment to ensure the application has time to initialize
    Start-Sleep -Seconds 2

    # Verify that the application is running
    $running = Get-Process -Name "eco_edge_monitor" -ErrorAction SilentlyContinue
    if (-not $running) {
        Write-Warning "Eco Edge Monitor failed to start. Please start it manually."
    }
}
else {
    Write-Warning "Monitor executable not found at $monitorExePath. The system tray application was not started."
}

Write-Host "Eco Edge Agent installation complete."
