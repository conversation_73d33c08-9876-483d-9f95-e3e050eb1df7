# Build script to create container images for packaging
$ErrorActionPreference = 'Stop'
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition

Write-Host "Building container images..."

# Build backend
Set-Location (Join-Path $scriptPath "containers\backend")
podman build -t eco-edge-service .
podman save -o (Join-Path $scriptPath "chocolateyinstall\eco-edge-service.tar") eco-edge-service

# Build frontend
Set-Location (Join-Path $scriptPath "containers\frontend")
podman build -t eco-edge-interface .
podman save -o (Join-Path $scriptPath "chocolateyinstall\eco-edge-interface.tar") eco-edge-interface

Write-Host "Container images have been built and saved successfully."