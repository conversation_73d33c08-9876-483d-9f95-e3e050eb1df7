# PowerShell installation script for Eco Edge Agent (Windows)
# 1. Check if <PERSON><PERSON> is installed; if not, install it
# 2. Prompt for config file (installer <PERSON><PERSON> will pass path as argument)
# 3. Load docker images from docker_tars (octopus-service.tar, ewp-edge.tar) using podman
# 4. Run backend and frontend containers with -e arguments and -v volumns arguments.
# if config.json file is provided use values from config.json file
# otherwise use default values
# 5. Copy/install eco-edge application files

param(
    [string]$ConfigFilePath,
    [switch]$EnableLogging = $true,
    [string]$LogPath = "",
    [int]$backendPort = 9080,
    [int]$frontendPort = 8080
)

# Setup logging
if ($EnableLogging) {
    if ([string]::IsNullOrEmpty($LogPath)) {
        # Use current directory instead of TEMP
        $currentDir = $PSScriptRoot
        $LogPath = Join-Path $currentDir "EcoEdgeAgent_Install_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
    }
    
    # Start transcript to capture all output
    Start-Transcript -Path $LogPath -Append
    Write-Host "Logging enabled. Log file: $LogPath"
}

# Helper function for logging
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    # Always write to console
    if ($Level -eq "ERROR") {
        Write-Host $logMessage -ForegroundColor Red
    }
    elseif ($Level -eq "WARNING") {
        Write-Host $logMessage -ForegroundColor Yellow
    }
    else {
        Write-Host $logMessage
    }
}

Write-Log "Starting Eco Edge Agent installation..."

# Step 1: Check Podman installation
Write-Log "Checking for Podman installation..."
$podmanInstalled = Get-Command podman -ErrorAction SilentlyContinue
if (-not $podmanInstalled) {
    Write-Log "Podman not found. Installing Podman..." "WARNING"
    $podmanMsiUrl = "https://github.com/containers/podman/releases/download/v5.5.0/podman-5.5.0-setup.exe"
    $installerPath = Join-Path $env:TEMP "podman-setup.exe"
    try {
        Write-Log "Downloading Podman installer from $podmanMsiUrl ..."
        Write-Log "Installer will be saved to: $installerPath"
        
        # Detailed error handling for download
        try {
            Invoke-WebRequest -Uri $podmanMsiUrl -OutFile $installerPath -UseBasicParsing
            
            if (Test-Path $installerPath) {
                $fileSize = (Get-Item $installerPath).Length
                Write-Log "Download completed. File size: $fileSize bytes"
            }
            else {
                Write-Log "Download appears to have failed. File not found at $installerPath" "ERROR"
                throw "Downloaded file not found"
            }
        }
        catch {
            Write-Log "Error during download: $($_)" "ERROR"
            throw "Download failed: $_"
        }
        
        Write-Log "Running Podman installer..."
        try {
            $process = Start-Process -FilePath $installerPath -ArgumentList "/install /quiet /norestart" -Wait -NoNewWindow -PassThru
            Write-Log "Installer process exit code: $($process.ExitCode)"
            
            # Handle specific error codes
            switch ($process.ExitCode) {
                0 { 
                    Write-Log "Podman installer completed successfully."
                }
                1618 { 
                    Write-Log "Error: Another installation is already in progress (Error 1618)." "ERROR"
                    Write-Log "Please wait for the other installation to complete or restart your computer and try again." "ERROR"
                    throw "Installation aborted: Another installation is already in progress"
                }
                1603 { 
                    Write-Log "Error: Fatal error during installation (Error 1603)." "ERROR"
                    throw "Installation aborted: Fatal error during Podman installation"
                }
                1641 { 
                    Write-Log "The installer has initiated a restart. Please run the script again after restart." "WARNING"
                    exit 0
                }
                3010 { 
                    Write-Log "A system restart is required to complete the installation." "WARNING"
                    Write-Log "Please restart your computer and run this script again." "WARNING"
                    exit 0
                }
                default {
                    if ($process.ExitCode -ne 0) {
                        Write-Log "Installer returned non-zero exit code: $($process.ExitCode)" "ERROR"
                        throw "Installation aborted: Podman installer failed with exit code $($process.ExitCode)"
                    }
                }
            }
        }
        catch {
            Write-Log "Error starting installer process: $($_)" "ERROR"
            throw "Installer process failed: $_"
        }
        
        # Clean up installer file
        Remove-Item $installerPath -Force -ErrorAction SilentlyContinue
        Write-Log "Podman installation completed."
    }
    catch {
        Write-Log "Failed to download or install Podman: $($_)" "ERROR"
        exit 1
    }
    # Refresh PATH for current session
    Write-Log "Refreshing PATH environment variable..."
    try {
        $oldPath = $env:Path
        $env:Path = [System.Environment]::GetEnvironmentVariable('Path', [System.EnvironmentVariableTarget]::Machine)
        Write-Log "PATH refreshed successfully."
        
        # Log the PATH for debugging
        Write-Log "Current PATH: $env:Path"
    }
    catch {
        Write-Log "Error refreshing PATH: $($_)" "ERROR"
    }
    
    # Verify installation
    Write-Log "Verifying Podman installation..."
    $podmanInstalled = Get-Command podman -ErrorAction SilentlyContinue
    if (-not $podmanInstalled) {
        Write-Log "Podman installation failed or podman not found in PATH." "ERROR"
        
        # Try to find Podman in common installation locations
        $possiblePaths = @(
            "C:\Program Files\RedHat\Podman\podman.exe",
            "C:\ProgramData\chocolatey\bin\podman.exe",
            "C:\ProgramData\chocoportable\lib\podman-cli\tools\podman-5.5.0\usr\bin\podman.exe",
            "C:\ProgramData\chocoportable\lib\podman-cli\tools\podman-5.4.0\usr\bin\podman.exe"
        )
        
        foreach ($path in $possiblePaths) {
            if (Test-Path $path) {
                Write-Log "Found Podman at: $path, but it's not in PATH" "WARNING"
            }
        }
        
        exit 1
    }
    else {
        $podmanVersion = & podman version --format '{{.Client.Version}}' 2>$null
        Write-Log "Podman installed successfully. Version: $podmanVersion"
        
        # Initialize Podman machine with WSL
        Write-Log "Checking for existing Podman machine..."
        try {
            $machineExists = & podman machine list --format "{{.Name}}" 2>&1 | Select-String -Pattern "podman-machine-default" -Quiet
            
            if ($machineExists) {
                Write-Log "Podman machine already exists. Checking if it's running..."
                $machineRunning = & podman machine list --format "{{.Running}}" 2>&1 | Select-String -Pattern "true" -Quiet
                
                if (-not $machineRunning) {
                    Write-Log "Starting existing Podman machine..."
                    $startOutput = & podman machine start 2>&1
                    Write-Log "Machine start output: $startOutput"
                }
                else {
                    Write-Log "Podman machine is already running"
                }
            }
            else {
                Write-Log "Creating new Podman machine with WSL..."
                $initOutput = & podman machine init --now --rootful --cpus 2 --memory 2048 --disk-size 20 2>&1
                Write-Log "Machine initialization output: $initOutput"
                
                # Verify machine was created and is running
                $machineVerify = & podman machine list 2>&1
                Write-Log "Machine verification: $machineVerify"
            }
            
            # Test Podman connection
            Write-Log "Testing Podman connection..."
            $pingOutput = & podman info 2>&1
            Write-Log "Podman connection test completed successfully"
        }
        catch {
            Write-Log "Error initializing Podman machine: $($_)" "ERROR"
            Write-Log "Will attempt to continue installation, but container operations may fail" "WARNING"
        }
    }
    # Placeholder: Podman registry login (if needed)
    # Example: podman login <registry> --username <user> --password <pass>
    # TODO: Implement registry login if required

}
else {
    Write-Host "Podman is already installed."
}

# Step 2: Handle config file (already passed as $ConfigFilePath)
$installRoot = Split-Path -Parent $PSScriptRoot
$defaultConfigPath = Join-Path $installRoot "config.json"

if ($ConfigFilePath -and (Test-Path $ConfigFilePath)) {
    Write-Host "Using provided config file: $ConfigFilePath"
}
elseif (Test-Path $defaultConfigPath) {
    Write-Host "Using default config file found at: $defaultConfigPath"
    $ConfigFilePath = $defaultConfigPath
}
else {
    Write-Warning "No config file found! Using default values."
}

# Step 3: Load Docker images from .tar files
# Determine the installation root directory (parent of scripts folder)
$installRoot = Split-Path -Parent $PSScriptRoot
$dockerTarsDir = Join-Path $installRoot 'docker_tars'

Write-Log "Looking for docker images in: $dockerTarsDir"

# Verify docker_tars directory exists
if (-not (Test-Path $dockerTarsDir)) {
    Write-Log "Docker tars directory not found at: $dockerTarsDir" "ERROR"
    Write-Log "Installation directory structure: $(Get-ChildItem $installRoot | ForEach-Object { $_.Name })" "INFO"
    exit 1
}

# Function to check if a port is in use
function Test-PortInUse {
    param(
        [int]$Port
    )
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $result = $tcpClient.ConnectAsync('127.0.0.1', $Port).Wait(100)
        $tcpClient.Close()
        return $result
    }
    catch {
        return $false
    }
}

$images = @('octopus-service.tar', 'ewp.tar')
foreach ($img in $images) {
    $imgPath = Join-Path $dockerTarsDir $img
    if (Test-Path $imgPath) {
        $fileSize = (Get-Item $imgPath).Length
        Write-Log "Found image file: $imgPath (Size: $fileSize bytes)"
        Write-Log "Loading image $imgPath into Podman..."
        
        try {
            $output = & podman load -i $imgPath 2>&1
            Write-Log "Podman load output: $output"
        }
        catch {
            Write-Log "Error loading image: $($_)" "ERROR"
            Write-Log "Command output: $output" "ERROR"
            exit 1
        }
    }
    else {
        Write-Log "$imgPath not found! Installation cannot continue without required images." "ERROR"
        Write-Log "Files in docker_tars directory: $(Get-ChildItem $dockerTarsDir | ForEach-Object { $_.Name })" "INFO"
        exit 1
    }
}

# Step 4: Run backend and frontend containers with -e and -v arguments
# Define default env and volume maps
$backendEnvVarsMap = @{
    "REST_PORT"                         = "9080"
    "REST_HOST"                         = "0.0.0.0"
    "CLOUD_DEVICE_CERTIFICATE_PATH"     = "/app/certs/device_cert.pem"
    "CLOUD_DEVICE_PRIVATE_KEY_PATH"     = "/app/certs/device_key.pem"
    "CLOUD_CONNECTION_CERT_PATH"        = "/app/certs/connectionCertificate.pem"
    "CLOUD_CONNECTION_PRIVATE_KEY_PATH" = "/app/certs/privateKey.pem"
    "CLOUD_CONNECTION_STRING_PATH"      = "/app/certs/connectionString.txt"
    "CLOUD_BOOTSTRAP_SERVER_URL"        = "boot-strap-url"  # TODO: Change this value from CICD
    "DB_PATH"                           = "/app/db"
    "AUTH_DB_PATH"                      = "/app/db/auth/auth.db"
    "AUTH_CONFIG_DB_PATH"               = "/app/db/auth/config.db"
    "SNMP_CONFIG_DB_PATH"               = "/app/db/snmp/config.db"
}

$frontendEnvVarsMap = @{
    "API_URL"    = "http://localhost:9080"
    "NGINX_PORT" = "80"
}

# Default volume mounts (only for backend)
# Determine the installation root directory for volume mounts
$installRoot = Split-Path -Parent $PSScriptRoot
$dataDir = Join-Path $installRoot "data"

# Create data directory if it doesn't exist
if (-not (Test-Path $dataDir)) {
    try {
        New-Item -ItemType Directory -Path $dataDir -Force | Out-Null
        Write-Host "Created data directory: $dataDir"
    }
    catch {
        Write-Warning "Failed to create data directory: $($_)"
    }
}

$backendVolumesMap = @{
    # Format: "host_path" = "container_path"
    # "$dataDir" = "/app/data"
}

# Helper: Merge config env/vols into defaults
function Merge-EnvVars {
    param(
        [hashtable]$defaultMap,
        $override
    )
    $merged = @{}
    foreach ($k in $defaultMap.Keys) {
        $merged[$k] = $defaultMap[$k]
    }
    
    if ($override) {
        # Handle PSCustomObject from JSON conversion
        if ($override.PSObject.Properties) {
            Write-Log "Processing environment variables from PSCustomObject"
            foreach ($prop in $override.PSObject.Properties) {
                $key = $prop.Name
                $value = $prop.Value
                Write-Log "Adding environment variable: $key=$value"
                $merged[$key] = $value
            }
        }
        # Handle regular hashtable
        elseif ($override -is [System.Collections.IDictionary]) {
            Write-Log "Processing environment variables from hashtable"
            foreach ($key in $override.Keys) {
                Write-Log "Adding environment variable: $key=$($override[$key])"
                $merged[$key] = $override[$key]
            }
        }
        else {
            Write-Log "Unknown environment variable format. Using defaults." "WARNING"
        }
    }
    
    return $merged
}

function Merge-Volumes {
    param(
        [hashtable]$defaultMap,
        $override
    )
    $merged = @{}
    foreach ($k in $defaultMap.Keys) {
        $merged[$k] = $defaultMap[$k]
    }
    
    if ($override) {
        # Handle hashtable/object format (container_path: host_path)
        if ($override -is [System.Collections.IDictionary] -or $override.PSObject.Properties) {
            Write-Log "Processing volume mappings from hashtable format"
            # If it's a PSObject from JSON conversion
            if ($override.PSObject.Properties) {
                foreach ($prop in $override.PSObject.Properties) {
                    $containerPath = $prop.Name
                    $hostPath = $prop.Value
                    Write-Log "Adding volume mapping: $hostPath -> $containerPath"
                    $merged[$hostPath] = $containerPath
                }
            }
            # If it's a regular hashtable
            else {
                foreach ($containerPath in $override.Keys) {
                    $hostPath = $override[$containerPath]
                    Write-Log "Adding volume mapping: $hostPath -> $containerPath"
                    $merged[$hostPath] = $containerPath
                }
            }
        }
        # Handle array format (strings like "host_path:container_path")
        elseif ($override -is [array]) {
            Write-Log "Processing volume mappings from array format"
            foreach ($vol in $override) {
                # Parse host_path:container_path
                if ($vol -match "^(.+?):(.+)$") {
                    $hostPath = $Matches[1]
                    $containerPath = $Matches[2]
                    Write-Log "Adding volume mapping: $hostPath -> $containerPath"
                    $merged[$hostPath] = $containerPath
                }
            }
        }
        else {
            Write-Log "Unknown volume mapping format. Using defaults." "WARNING"
        }
    }
    
    return $merged
}

$backendEnv = $backendEnvVarsMap
$frontendEnv = $frontendEnvVarsMap
$backendVolumes = $backendVolumesMap

if ($ConfigFilePath -and (Test-Path $ConfigFilePath)) {
    try {
        $config = Get-Content $ConfigFilePath | ConvertFrom-Json
        if ($config.backendEnv) {
            $backendEnv = Merge-EnvVars $backendEnvVarsMap $config.backendEnv
        }
        if ($config.frontendEnv) {
            $frontendEnv = Merge-EnvVars $frontendEnvVarsMap $config.frontendEnv
        }
        if ($config.backendVol) {
            # When config file provides volume mappings, use only those instead of merging with defaults
            Write-Log "Using volume mappings from config file"
            $backendVolumes = @{}
            $backendVolumes = Merge-Volumes $backendVolumes $config.backendVol
        }
    }
    catch {
        Write-Warning "Failed to parse config file. Using default values."
        Write-Log "Error parsing config file: $($_)" "ERROR"
    }
}

# Build env and volume args for backend and frontend
$backendEnvArgs = @()
foreach ($key in $backendEnv.Keys) {
    $backendEnvArgs += "-e $key=$($backendEnv[$key])"
}

$frontendEnvArgs = @()
foreach ($key in $frontendEnv.Keys) {
    $frontendEnvArgs += "-e $key=$($frontendEnv[$key])"
}
# Create host directories for volume mappings if they don't exist
Write-Log "Checking and creating host directories for volume mappings..."
foreach ($hostPath in $backendVolumes.Keys) {
    if (-not (Test-Path $hostPath)) {
        try {
            Write-Log "Creating directory: $hostPath"
            New-Item -ItemType Directory -Path $hostPath -Force -ErrorAction Stop | Out-Null
        }
        catch {
            Write-Log "Failed to create directory '$hostPath': $($_)" "ERROR"
            Write-Log "Volume mapping may fail for this path" "WARNING"
        }
    }
    else {
        Write-Log "Directory already exists: $hostPath"
    }
}

$volArgs = @()
foreach ($hostPath in $backendVolumes.Keys) {
    $volArgs += "-v ${hostPath}:$($backendVolumes[$hostPath])"
}


# Check if eco-edge-network exists, create if not
Write-Log "Checking if eco-edge-network exists..."
try {
    $networkExists = & podman network ls --format "{{.Name}}" 2>&1 | Select-String -Pattern "^eco-edge-network$" -Quiet
    if (-not $networkExists) {
        Write-Log "Creating eco-edge-network..."
        $output = & podman network create eco-edge-network 2>&1
        Write-Log "Network creation output: $output"
    }
    else {
        Write-Log "eco-edge-network already exists"
    }
    
    # List all networks for debugging
    $networks = & podman network ls 2>&1
    Write-Log "Available networks:\n$networks"
}
catch {
    Write-Log "Error managing Podman networks: $($Error[0].Message)" "ERROR"
    Write-Log "This may indicate Podman is not properly installed or configured" "WARNING"
    
    # Try to get Podman version for debugging
    try {
        $podmanVersion = & podman version 2>&1
        Write-Log "Podman version information:\n$podmanVersion"
    }
    catch {
        Write-Log "Failed to get Podman version: $($_)" "ERROR"
    }
}

# Check if ports are in use and by which processes
Write-Log "Checking if required ports are in use..."

# Function to get process using a port
function Get-ProcessUsingPort {
    param(
        [int]$Port
    )
    
    try {
        # Using netstat to find processes using specific ports
        $netstatOutput = & netstat -ano | Select-String -Pattern ":$Port\s" | Select-String -Pattern "LISTENING"
        
        if ($netstatOutput) {
            # Extract PID from the netstat output
            $pidMatch = $netstatOutput -match '\s+(\d+)$'
            if ($pidMatch) {
                $processPid = $matches[1]
                
                # Get process name from PID
                $process = Get-Process -Id $processPid -ErrorAction SilentlyContinue
                if ($process) {
                    return @{
                        "PID" = $processPid
                        "ProcessName" = $process.ProcessName
                        "Path" = $process.Path
                    }
                }
                return @{
                    "PID" = $processPid
                    "ProcessName" = "Unknown"
                    "Path" = "Unknown"
                }
            }
        }
        return $null
    }
    catch {
        Write-Log "Error checking process using port $Port: $($_.Exception.Message)" "WARNING"
        return $null
    }
}

# Function to check if port is used by a Podman container and get container details
function Test-PortUsedByPodmanContainer {
    param(
        [int]$Port
    )
    
    try {
        # Check if port is in use
        if (-not (Test-PortInUse -Port $Port)) {
            return @{ Used = $false; ContainerName = $null; ContainerId = $null }
        }
        
        # Get detailed container information using JSON format
        $containersJson = & podman ps --format json 2>&1
        if ($containersJson -and $containersJson -ne "[]") {
            try {
                $containers = $containersJson | ConvertFrom-Json
                
                # Loop through containers to check port mappings
                foreach ($container in $containers) {
                    # Check if this container has ports configured
                    if ($container.Ports -and $container.Ports.Length -gt 0) {
                        foreach ($portMapping in $container.Ports) {
                            # Get host port from the mapping
                            $hostPort = $portMapping.host_port
                            if ($hostPort -eq $Port) {
                                return @{ 
                                    Used = $true
                                    ContainerName = $container.Names[0]
                                    ContainerId = $container.Id
                                }
                            }
                        }
                    }
                }
            }
            catch {
                Write-Log "Error parsing container JSON: $($_)" "WARNING"
            }
        }
        
        # Fallback to process checking if JSON method fails
        $process = Get-ProcessUsingPort -Port $Port
        if ($process -and ($process.ProcessName -like "*podman*" -or 
                          $process.ProcessName -eq "containerd" -or 
                          $process.ProcessName -eq "containerd-shim" -or 
                          $process.ProcessName -eq "catatonit")) {
            return @{ Used = $true; ContainerName = "Unknown"; ContainerId = "Unknown" }
        }
        
        return @{ Used = $false; ContainerName = $null; ContainerId = $null }
    }
    catch {
        Write-Log "Error checking if port $Port is used by Podman: $($_.Exception.Message)" "WARNING"
        return @{ Used = $false; ContainerName = $null; ContainerId = $null }
    }
}

# Check frontend port
$frontendPortInUse = Test-PortInUse -Port $frontendPort
if ($frontendPortInUse) {
    $frontendContainerInfo = Test-PortUsedByPodmanContainer -Port $frontendPort
    $frontendProcess = Get-ProcessUsingPort -Port $frontendPort
    
    if ($frontendContainerInfo.Used) {
        Write-Log "Frontend port $frontendPort is in use by a Podman container: $($frontendContainerInfo.ContainerName) ($($frontendContainerInfo.ContainerId)). Will attempt to stop and remove it." "INFO"
        $frontendPortOwnedByUs = $true
    }
    else {
        Write-Log "Frontend port $frontendPort is in use by process: $($frontendProcess.ProcessName) (PID: $($frontendProcess.PID))" "WARNING"
        $frontendPortOwnedByUs = $false
    }
}
else {
    $frontendPortOwnedByUs = $false
}

# Check backend port
$backendPortInUse = Test-PortInUse -Port $backendPort
if ($backendPortInUse) {
    $backendContainerInfo = Test-PortUsedByPodmanContainer -Port $backendPort
    $backendProcess = Get-ProcessUsingPort -Port $backendPort
    
    if ($backendContainerInfo.Used) {
        Write-Log "Backend port $backendPort is in use by a Podman container: $($backendContainerInfo.ContainerName) ($($backendContainerInfo.ContainerId)). Will attempt to stop and remove it." "INFO"
        $backendPortOwnedByUs = $true
    }
    else {
        Write-Log "Backend port $backendPort is in use by process: $($backendProcess.ProcessName) (PID: $($backendProcess.PID))" "WARNING"
        $backendPortOwnedByUs = $false
    }
}
else {
    $backendPortOwnedByUs = $false
}

# Check if either port is in use by a non-Podman process and exit if so
if (-not $frontendPortOwnedByUs -and $frontendPortInUse) {
    $errorMsg = "Frontend port $frontendPort is in use by another application: $($frontendProcess.ProcessName) (PID: $($frontendProcess.PID)). Installation cannot proceed."
    Write-Log $errorMsg "ERROR"
    throw $errorMsg
}

if (-not $backendPortOwnedByUs -and $backendPortInUse) {
    $errorMsg = "Backend port $backendPort is in use by another application: $($backendProcess.ProcessName) (PID: $($backendProcess.PID)). Installation cannot proceed."
    Write-Log $errorMsg "ERROR"
    throw $errorMsg
}

# Check if our containers exist
Write-Log "Checking for existing containers..."
$containers = @("octopus-service", "ewp-edge")
foreach ($container in $containers) {
    # Check if container exists
    try {
        $containerExists = & podman ps -a --format "{{.Names}}" 2>&1 | Select-String -Pattern "^$container$" -Quiet

        if ($containerExists) {
            $ourContainersExist = $true
            Write-Log "Found existing container: $container"

            # Check if container is running
            $containerRunning = & podman ps --format "{{.Names}}" 2>&1 | Select-String -Pattern "^$container$" -Quiet

            if ($containerRunning) {
                Write-Log "Stopping container: $container"
                $stopOutput = & podman stop $container 2>&1
                Write-Log "Stop container output: $stopOutput"
            }

            Write-Log "Removing container: $container"
            $rmOutput = & podman rm $container 2>&1
            Write-Log "Remove container output: $rmOutput"
        }
        else {
            Write-Log "Container $container does not exist, no need to remove"
        }
    }
    catch {
        Write-Log "Error managing container $container`: $($_.Exception.Message)" "ERROR"
    }
}

# Check if ports are still in use after stopping our containers
Write-Log "Checking if ports are still in use after stopping our containers..."
try {
    # Give a moment for ports to be released
    Start-Sleep -Seconds 2
    
    $frontendStillInUse = Test-PortInUse -Port $frontendPort
    $backendStillInUse = Test-PortInUse -Port $backendPort
    
    # If frontend port is still in use and wasn't owned by our containers, throw an error
    if ($frontendStillInUse -and -not $frontendPortOwnedByUs) {
        $errorMsg = "Frontend port $frontendPort is in use by another application. Please choose a different port or close the application using this port."
        Write-Log $errorMsg "ERROR"
        throw $errorMsg
    }
    # If frontend port is still in use even though it was owned by our containers (which we've stopped), throw an error
    elseif ($frontendStillInUse -and $frontendPortOwnedByUs) {
        $errorMsg = "Frontend port $frontendPort is still in use even after stopping our containers. This may indicate another application has claimed the port."
        Write-Log $errorMsg "ERROR"
        throw $errorMsg
    }
    
    # If backend port is still in use and wasn't owned by our containers, throw an error
    if ($backendStillInUse -and -not $backendPortOwnedByUs) {
        $errorMsg = "Backend port $backendPort is in use by another application. Please choose a different port or close the application using this port."
        Write-Log $errorMsg "ERROR"
        throw $errorMsg
    }
    # If backend port is still in use even though it was owned by our containers (which we've stopped), throw an error
    elseif ($backendStillInUse -and $backendPortOwnedByUs) {
        $errorMsg = "Backend port $backendPort is still in use even after stopping our containers. This may indicate another application has claimed the port."
        Write-Log $errorMsg "ERROR"
        throw $errorMsg
    }
    
    Write-Log "Ports are available: Frontend=$frontendPort, Backend=$backendPort"
}
catch {
    if ($_.Exception.Message -like "*port*is*in use*") {
        # This is a port availability error we've already logged
        exit 1
    }
    else {
        Write-Log "Error checking port availability: $_" "ERROR"
        exit 1
    }
}

# Run octopus-service container
Write-Log "Running octopus-service container..."
$podmanRunArgs = @("-d", "--name", "octopus-service", "--restart", "always", "--network", "eco-edge-network", "--network-alias", "octopus-service", "--hostname", "octopus-service", "-p", "0.0.0.0:${backendPort}:9080")

# Add environment arguments
if ($backendEnvArgs.Count -gt 0) {
    $podmanRunArgs += $backendEnvArgs
    Write-Log "Added backend environment arguments: $($backendEnvArgs -join ' ')"
}

# Add volume arguments only if there are any
if ($volArgs.Count -gt 0) {
    $podmanRunArgs += $volArgs
    Write-Log "Added volume arguments: $($volArgs -join ' ')"
}

$podmanRunArgs += @("--user", "1000:1000", "octopus-service:latest")

# Convert array to command string and execute
$podmanRunCommand = "podman run " + ($podmanRunArgs -join " ")
Write-Log "Executing command: $podmanRunCommand"

try {
    $output = Invoke-Expression "$podmanRunCommand 2>&1"
    Write-Log "Container start output: $output"
    
    # Verify container is running
    Start-Sleep -Seconds 2
    $containerRunning = & podman ps --format "{{.Names}}" 2>&1 | Select-String -Pattern "^octopus-service$" -Quiet
    if ($containerRunning) {
        Write-Log "octopus-service container started successfully"
        Write-Log "Backend service running on port $backendPort"
    }
    else {
        Write-Log "octopus-service container failed to start or stopped immediately" "ERROR"
        $logs = & podman logs octopus-service 2>&1
        Write-Log "Container logs:\n$logs" "ERROR"
    }
}
catch {
    Write-Log "Error starting octopus-service container: $($_)" "ERROR"
}

# Run ewp-edge container
Write-Log "Running ewp-edge container..."
$podmanRunArgs = @("-d", "--name", "ewp-edge", "--restart", "always", "--network", "eco-edge-network", "--network-alias", "ewp-edge", "--hostname", "ewp-edge", "-p", "0.0.0.0:${frontendPort}:80")

# Add environment arguments
if ($frontendEnvArgs.Count -gt 0) {
    $podmanRunArgs += $frontendEnvArgs
    Write-Log "Added frontend environment arguments: $($frontendEnvArgs -join ' ')"
}

$podmanRunArgs += @("ewp-edge:latest")

# Convert array to command string and execute
$podmanRunCommand = "podman run " + ($podmanRunArgs -join " ")
Write-Log "Executing command: $podmanRunCommand"

try {
    $output = Invoke-Expression "$podmanRunCommand 2>&1"
    Write-Log "Container start output: $output"
    
    # Verify container is running
    Start-Sleep -Seconds 2
    $containerRunning = & podman ps --format "{{.Names}}" 2>&1 | Select-String -Pattern "^ewp-edge$" -Quiet
    if ($containerRunning) {
        Write-Log "ewp-edge container started successfully"
        Write-Log "Frontend service running on port $frontendPort"
    }
    else {
        Write-Log "ewp-edge container failed to start or stopped immediately" "ERROR"
        $logs = & podman logs ewp-edge 2>&1
        Write-Log "Container logs:\n$logs" "ERROR"
    }
}
catch {
    Write-Log "Error starting ewp-edge container: $($_)" "ERROR"
}

# Step 5: Install Eco Edge Monitor system tray application
Write-Log "Installing Eco Edge Monitor system tray application..."

# Determine the installation directory based on the current script location
$scriptDir = Split-Path -Parent $PSScriptRoot
$installDir = Split-Path -Parent $scriptDir

Write-Log "Using installation directory: $installDir"
$monitorDir = Join-Path $installDir "eco-edge\monitor"

# Create directory with proper error handling
if (-not (Test-Path $monitorDir)) {
    try {
        New-Item -ItemType Directory -Path $monitorDir -Force -ErrorAction Stop | Out-Null
        Write-Log "Created directory: $monitorDir"
    }
    catch {
        Write-Log "Failed to create directory '$monitorDir': $($_)" "ERROR"
        Write-Log "Make sure you're running with administrator privileges." "WARNING"
        
        # Check directory permissions
        try {
            $parentDir = Split-Path -Parent $monitorDir
            $acl = Get-Acl -Path $parentDir -ErrorAction SilentlyContinue
            Write-Log "Parent directory permissions: $($acl.AccessToString)" "INFO"
        }
        catch {
            Write-Log "Could not check directory permissions: $($_)" "WARNING"
        }
    }
}

# Copy system tray application files
$installRoot = Split-Path -Parent $PSScriptRoot
$monitorSourceDir = Join-Path $installRoot "eco-edge"
Write-Log "Looking for eco-edge monitor files in: $monitorSourceDir"

# List directory contents for debugging
Write-Log "Install root directory contents: $(Get-ChildItem $installRoot | ForEach-Object { $_.Name })" "INFO"

if (Test-Path $monitorSourceDir) {
    # Count files to verify there's actually content to copy
    $fileCount = (Get-ChildItem -Path $monitorSourceDir -Recurse -File).Count
    Write-Log "Found $fileCount files in source directory"
    
    if ($fileCount -gt 0) {
        Write-Log "Copying eco-edge monitor files from $monitorSourceDir to $monitorDir"
        
        # First check if the source directory has any locked files
        try {
            Write-Log "Checking for locked files in source directory..."
            # Try to create a temporary copy of the source directory to test for locks
            $tempDir = Join-Path $env:TEMP "eco_edge_temp_$(Get-Random)"
            
            # Create the temp directory
            New-Item -ItemType Directory -Path $tempDir -Force -ErrorAction Stop | Out-Null
            
            # Try to copy files - this will fail if files are locked
            try {
                # Use robocopy for better handling of locked files
                Write-Log "Using robocopy for more reliable file copying"
                $robocopyOutput = & robocopy "$monitorSourceDir" "$monitorDir" /E /R:2 /W:1 /NFL /NDL /NJH /NJS /NC /NS /MT:8 2>&1
                Write-Log "Files copied successfully"
            }
            catch {
                Write-Log "Robocopy error: $($_)" "WARNING"
                
                # Fall back to individual file copying if robocopy fails
                Write-Log "Falling back to individual file copying" "WARNING"
                Get-ChildItem -Path $monitorSourceDir -Recurse -File | ForEach-Object {
                    $relativePath = $_.FullName.Substring($monitorSourceDir.Length + 1)
                    $targetPath = Join-Path $monitorDir $relativePath
                    $targetDir = Split-Path -Parent $targetPath
                    
                    # Create target directory if it doesn't exist
                    if (-not (Test-Path $targetDir)) {
                        New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
                    }
                    
                    # Try to copy each file individually, skipping locked ones
                    try {
                        Copy-Item -Path $_.FullName -Destination $targetPath -Force -ErrorAction Stop
                    }
                    catch {
                        Write-Log "Skipping locked file: $($_.FullName)" "WARNING"
                    }
                }
            }
            
            # Clean up temp directory
            Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue
        }
        catch {
            Write-Log "Error during file copy preparation: $($_)" "ERROR"
        }
    }
    else {
        Write-Log "Source directory exists but contains no files" "WARNING"
    }
}
else {
    Write-Log "System tray application files not found at $monitorSourceDir. The system tray application will not be installed." "WARNING"
}

# Create startup shortcut for system tray application
Write-Log "Setting up startup shortcut..."

$startupPath = [Environment]::GetFolderPath("Startup")
$monitorExePath = Join-Path $monitorDir "eco_edge_monitor.exe"
$startupShortcutFile = Join-Path $startupPath "Eco Edge Monitor.lnk"

Write-Log "Startup path: $startupPath"
Write-Log "Monitor executable path: $monitorExePath"
Write-Log "Shortcut file path: $startupShortcutFile"

if (Test-Path $monitorExePath) {
    Write-Log "Monitor executable found, creating shortcut"
    try {
        $WshShell = New-Object -comObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut($startupShortcutFile)
        $Shortcut.TargetPath = $monitorExePath
        $Shortcut.Description = "Eco Edge Monitor"
        $Shortcut.WorkingDirectory = $monitorDir
        $Shortcut.Save()
        Write-Log "Startup shortcut created successfully"
    }
    catch {
        Write-Log "Could not create startup shortcut: $($_)" "WARNING"
    }
}
else {
    Write-Log "Monitor executable not found at $monitorExePath" "WARNING"
}

# Check for monitor executable path and start application
$monitorExePath = Join-Path $monitorDir "eco_edge_monitor.exe"
if (Test-Path $monitorExePath) {
    # Kill any existing instances of the application and ensure all related processes are stopped
    try {
        # First attempt: Find and kill eco_edge_monitor processes
        $processes = Get-Process -Name "eco_edge_monitor" -ErrorAction SilentlyContinue
        if ($processes) {
            Write-Log "Found existing Eco Edge Monitor processes, stopping them"
            $processes | ForEach-Object { 
                try {
                    Write-Log "Stopping process with ID $($_.Id)"
                    $_.Kill()
                    $_.WaitForExit(2000) # Wait up to 2 seconds for process to exit
                    Write-Log "Process with ID $($_.Id) stopped"
                }
                catch {
                    Write-Log "Failed to stop process with ID $($_.Id): $($_)" "WARNING"
                }
            }
            Write-Log "Stopped existing Eco Edge Monitor instances."
            
            # Wait longer to ensure all resources are released
            Write-Log "Waiting for resources to be released..."
            Start-Sleep -Seconds 3
            
            # Second attempt: Check if any processes are still running
            $remainingProcesses = Get-Process -Name "eco_edge_monitor" -ErrorAction SilentlyContinue
            if ($remainingProcesses) {
                Write-Log "Some processes are still running. Attempting to force kill..." "WARNING"
                $remainingProcesses | ForEach-Object {
                    try {
                        # Force kill with Stop-Process
                        Stop-Process -Id $_.Id -Force -ErrorAction Stop
                        Write-Log "Force killed process with ID $($_.Id)"
                    }
                    catch {
                        Write-Log "Failed to force kill process with ID $($_.Id): $($_)" "ERROR"
                    }
                }
                # Wait again after force kill
                Start-Sleep -Seconds 2
            }
        }
        else {
            Write-Log "No existing Eco Edge Monitor processes found"
        }
        
        # Check for any file locks in the target directory
        if (Test-Path $monitorDir) {
            Write-Log "Checking for locked files in $monitorDir"
            try {
                # Try to rename a directory to itself - this will fail if any files are locked
                Rename-Item -Path $monitorDir -NewName "$monitorDir.temp" -ErrorAction Stop
                Rename-Item -Path "$monitorDir.temp" -NewName $monitorDir -ErrorAction Stop
                Write-Log "No locked files detected"
            }
            catch {
                Write-Log "Detected locked files in the target directory. Waiting additional time..." "WARNING"
                Start-Sleep -Seconds 5
            }
        }
    }
    catch {
        Write-Log "Could not stop existing Eco Edge Monitor processes: $($_)" "WARNING"
    }

    # Start the system tray application
    Write-Log "Starting Eco Edge Monitor application"
    try {
        $process = Start-Process -FilePath $monitorExePath -WorkingDirectory $monitorDir -PassThru
        Write-Log "Started Eco Edge Monitor with process ID: $($process.Id)"
        Write-Log "Eco Edge Monitor system tray application has been installed and started."
    }
    catch {
        Write-Log "Failed to start Eco Edge Monitor: $($_)" "ERROR"
    }

    # Wait a moment to ensure the application has time to initialize
    Start-Sleep -Seconds 2

    # Verify that the application is running
    try {
        $running = Get-Process -Name "eco_edge_monitor" -ErrorAction SilentlyContinue
        if (-not $running) {
            Write-Log "Eco Edge Monitor failed to start. Please start it manually." "WARNING"
        }
        else {
            Write-Log "Verified Eco Edge Monitor is running with process ID: $($running.Id)"
        }
    }
    catch {
        Write-Log "Error checking if Eco Edge Monitor is running: $($_)" "WARNING"
    }
}
else {
    Write-Log "Monitor executable not found at $monitorExePath. The system tray application was not started." "WARNING"
}

Write-Log "Eco Edge Agent installation complete."

# End transcript logging if it was started
if ($EnableLogging) {
    try {
        Stop-Transcript
        Write-Host "Installation log saved to: $LogPath"
    }
    catch {
        Write-Host "Could not properly close log file: $($_)"
    }
}

