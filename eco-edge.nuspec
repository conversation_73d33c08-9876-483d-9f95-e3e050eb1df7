<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2015/06/nuspec.xsd">
  <metadata>
    <id>eco-edge</id>
    <version>1.1.2</version>
    <title>Eco Edge Application</title>
    <authors>Eco Edge Team</authors>
    <description>Eco Edge application for efficient edge computing and monitoring.

This package will automatically install <PERSON>dman if it's not already installed.

Features:
- Web-based frontend interface for monitoring and control
- RESTful API backend for data processing
- System tray application for quick access and status monitoring

You can customize the installation using the following parameters:

Port Configuration:
- frontendPort: The port for the frontend interface (default: 8080)
- backendPort: The port for the backend API (default: 9080)

Unified Configuration (Recommended):

- configFile: Path to a unified configuration file containing all settings (environment variables and volume mounts)
  Supported file formats:
  - JSON files (.json): Structured format with sections for backendEnv, frontendEnv, and backendVol
  - .env files (.env): Prefixed KEY=VALUE format (BACKEND_ENV_*, FRONTEND_ENV_*, BACKEND_VOL_*)
    Note: The prefixes are mandatory for the unified configuration file to distinguish between different types of settings

Legacy Configuration Options:

Environment Variables:

1. Individual Environment Variables:
   - backendEnv:NAME=VALUE: Set individual environment variables for the backend service
   - frontendEnv:NAME=VALUE: Set individual environment variables for the frontend service

2. JSON-formatted Environment Variables:
   - backendEnvJson: JSON string containing environment variables for the backend service
   - frontendEnvJson: JSON string containing environment variables for the frontend service

3. File-based Environment Variables:
   - backendEnvFile: Path to a file containing environment variables for the backend service
   - frontendEnvFile: Path to a file containing environment variables for the frontend service

   Supported file formats:
   - JSON files (.json): Standard JSON format with key-value pairs
   - .env files (.env): Simple KEY=VALUE format, one per line

Volume Mounts (backend service only):

1. Individual Volume Mounts:
   - backendVol:CONTAINER_PATH=HOST_PATH: Mount a host directory to a container path for the backend service

2. JSON-formatted Volume Mounts:
   - backendVolJson: JSON string containing volume mounts for the backend service

3. File-based Volume Mounts:
   - backendVolFile: Path to a JSON file containing volume mounts for the backend service

Note:
- Host directories will be created automatically if they don't exist
- Volume mounts are only supported for the backend service, not the frontend service
- The backend service runs with user ID 1000:1000

Examples:

- Basic installation with custom ports:
  choco install eco-edge -y --params "'/frontendPort:9090 /backendPort:5000'"

- Installation with unified configuration file (recommended):
  choco install eco-edge -y --params "'/configFile=C:\path\to\unified-config.json'"

- Installation with unified configuration in .env format:
  choco install eco-edge -y --params "'/configFile=C:\path\to\unified-config.env'"

- Installation with individual environment variables:
  choco install eco-edge -y --params "'/backendEnv:DEBUG=true /backendEnv:LOG_LEVEL=debug'"

- Installation with JSON-formatted environment variables:
  choco install eco-edge -y --params "'/backendEnvJson={\"NODE_ENV\":\"development\",\"DEBUG\":\"true\",\"LOG_LEVEL\":\"debug\"}'"

- Installation with environment variables from JSON files:
  choco install eco-edge -y --params "'/backendEnvFile=C:\path\to\backend-env.json /frontendEnvFile=C:\path\to\frontend-env.json'"

- Installation with environment variables from .env files:
  choco install eco-edge -y --params "'/backendEnvFile=C:\path\to\backend.env /frontendEnvFile=C:\path\to\frontend.env'"

- Installation with volume mounts (backend only):
  choco install eco-edge -y --params "'/backendVol:/app/logs=C:\data\backend\logs'"

- Installation with volume mounts from JSON files (backend only):
  choco install eco-edge -y --params "'/backendVolFile=C:\path\to\backend-volumes.json'"

- Combined configuration with environment variables and volumes (legacy approach):
  choco install eco-edge -y --params "'/frontendPort:9090 /backendPort:5000 /backendEnvJson={\"NODE_ENV\":\"development\"} /backendVolFile=C:\path\to\backend-volumes.json'"

The system tray application will be installed and configured to start automatically with Windows.</description>
    <tags>eco-edge edge-computing monitoring</tags>
  </metadata>
  <files>
    <file src="tools\**" target="tools" />
  </files>
</package>
