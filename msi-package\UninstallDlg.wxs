<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
   <Fragment>
      <UI>
         <Dialog Id="UninstallOptionsDlg" Width="370" Height="270" Title="Uninstall Options">
            <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="!(loc.WixUINext)">
               <!-- Set the properties based on checkbox values when Next is clicked -->
               <Publish Property="KEEP_USER_DATA" Value="[KEEP_USER_DATA_CHECKBOX]">1</Publish>
               <Publish Property="KEEP_CONTAINERS" Value="[KEEP_CONTAINERS_CHECKBOX]">1</Publish>
               <Publish Property="KEEP_IMAGES" Value="[KEEP_IMAGES_CHECKBOX]">1</Publish>

               <!-- Log the values for debugging -->
               <Publish Property="KEEP_USER_DATA_LOG" Value="User data: [KEEP_USER_DATA_CHECKBOX]">1</Publish>
               <Publish Property="KEEP_CONTAINERS_LOG" Value="Containers: [KEEP_CONTAINERS_CHECKBOX]">1</Publish>
               <Publish Property="KEEP_IMAGES_LOG" Value="Images: [KEEP_IMAGES_CHECKBOX]">1</Publish>
            </Control>
            <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="!(loc.WixUIBack)" />
            <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="!(loc.WixUICancel)">
               <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
            </Control>

            <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes" Text="Uninstall Options" />
            <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes" Text="Select which components you want to keep during uninstallation." />
            <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="44" TabSkip="no" Text="!(loc.InstallDirDlgBannerBitmap)" />
            <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="370" Height="0" />
            <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="370" Height="0" />

            <Control Id="KeepUserDataLabel" Type="Text" X="20" Y="60" Width="290" Height="15" NoPrefix="yes" Text="Keep user data files:" />
            <Control Id="KeepUserDataCheckbox" Type="CheckBox" X="20" Y="80" Width="320" Height="18" Property="KEEP_USER_DATA_CHECKBOX" CheckBoxValue="1" Text="Keep user data files (database files, certificates, etc.)" />

            <Control Id="KeepContainersLabel" Type="Text" X="20" Y="110" Width="290" Height="15" NoPrefix="yes" Text="Keep containers:" />
            <Control Id="KeepContainersCheckbox" Type="CheckBox" X="20" Y="130" Width="320" Height="18" Property="KEEP_CONTAINERS_CHECKBOX" CheckBoxValue="1" Text="Keep running containers (octopus-service, ewp-edge)" />

            <Control Id="KeepImagesLabel" Type="Text" X="20" Y="160" Width="290" Height="15" NoPrefix="yes" Text="Keep container images:" />
            <Control Id="KeepImagesCheckbox" Type="CheckBox" X="20" Y="180" Width="320" Height="18" Property="KEEP_IMAGES_CHECKBOX" CheckBoxValue="1" Text="Keep container images (octopus-service, ewp-edge)" />
         </Dialog>
      </UI>
   </Fragment>
</Wix>
