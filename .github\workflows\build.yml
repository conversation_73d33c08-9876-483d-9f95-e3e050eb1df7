name: Build Eco Edge Package

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
  workflow_dispatch:

jobs:
  build:
    runs-on: windows-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
        cache: true
    
    - name: Install Flutter dependencies
      run: |
        cd eco_edge_monitor
        flutter pub get
    
    - name: Build Flutter application
      run: |
        cd eco_edge_monitor
        flutter build windows --release
    
    - name: Copy Flutter build to Chocolatey package
      run: |
        New-Item -ItemType Directory -Path "tools\chocolateyinstall\eco-edge-monitor" -Force
        Copy-Item -Path "eco_edge_monitor\build\windows\x64\runner\Release\*" -Destination "tools\chocolateyinstall\eco-edge-monitor" -Recurse -Force
    
    - name: Install Chocolatey
      run: |
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    
    - name: Create Chocolatey package
      run: |
        choco pack
    
    - name: Upload Chocolatey package
      uses: actions/upload-artifact@v3
      with:
        name: eco-edge-package
        path: '*.nupkg'
        retention-days: 7
