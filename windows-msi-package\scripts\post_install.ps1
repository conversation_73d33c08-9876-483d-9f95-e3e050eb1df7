# Post-installation script for Eco Edge Agent
# This script creates a scheduled task to run the installation with elevated privileges
# This approach is more reliable on Windows 11 which has stricter security policies

param(
    [Parameter(Mandatory=$true)]
    [string]$InstallPath
)

Write-Host "Starting Eco Edge Agent post-installation setup..."
Write-Host "Installation directory: $InstallPath"

# Define paths
$scriptsDir = Join-Path $InstallPath "scripts"
$setupBatPath = Join-Path $scriptsDir "setup.bat"
$installPs1Path = Join-Path $scriptsDir "install.ps1"
$taskName = "EcoEdgeAgent_Installation"

# Make sure paths exist
if (-not (Test-Path $setupBatPath)) {
    Write-Error "Setup batch file not found at: $setupBatPath"
    exit 1
}

if (-not (Test-Path $installPs1Path)) {
    Write-Error "Installation PowerShell script not found at: $installPs1Path"
    exit 1
}

# Check if we're running with admin privileges
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if ($isAdmin) {
    # We have admin rights, run the installation directly
    Write-Host "Running with administrator privileges. Starting installation directly..."
    try {
        Start-Process -FilePath "cmd.exe" -ArgumentList "/c `"$setupBatPath`"" -NoNewWindow -Wait
        Write-Host "Installation process started successfully."
    }
    catch {
        Write-Error "Failed to start installation process: $_"
        exit 1
    }
}
else {
    # We don't have admin rights, create a scheduled task to run with highest privileges
    Write-Host "Creating scheduled task to run installation with administrator privileges..."
    
    # Create the task action to run setup.bat
    $action = New-ScheduledTaskAction -Execute "cmd.exe" -Argument "/c `"$setupBatPath`""
    
    # Task will run immediately when registered
    $trigger = New-ScheduledTaskTrigger -Once -At (Get-Date).AddSeconds(10)
    
    # Task settings
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -RunOnlyIfNetworkAvailable -Hidden
    
    # Task principal (run with highest privileges)
    $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
    
    # Register the task
    try {
        Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Force
        Write-Host "Scheduled task created successfully. Installation will begin shortly."
        
        # Start the task immediately
        Start-ScheduledTask -TaskName $taskName
        Write-Host "Installation task started successfully."
    }
    catch {
        Write-Error "Failed to create scheduled task: $_"
        
        # Alternative approach: Try to launch elevated process
        Write-Host "Attempting to launch installer directly with elevation prompt..."
        try {
            Start-Process -FilePath "powershell.exe" -ArgumentList "-ExecutionPolicy Bypass -NoProfile -File `"$installPs1Path`"" -Verb RunAs
            Write-Host "Elevation prompt displayed to user."
        }
        catch {
            Write-Error "Failed to launch elevated process: $_"
            exit 1
        }
    }
}

Write-Host "Post-installation setup completed."
exit 0
