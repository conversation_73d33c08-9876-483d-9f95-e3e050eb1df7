**********************
PowerShell transcript start
Start time: 20250525161228
Username: APA\SESA808825
RunAs User: APA\SESA808825
Configuration Name: 
Machine: WIN01502063L (Microsoft Windows NT 10.0.22631.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -noexit -command try { . "c:\Users\<USER>\AppData\Local\Programs\Windsurf\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch {}
Process ID: 42304
PSVersion: 7.5.0
PSEdition: Core
GitCommitId: 7.5.0
OS: Microsoft Windows 10.0.22631
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161228.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161228.log
[2025-05-25 16:12:28] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 16:12:28] [INFO] Checking for Podman installation...
Podman is already installed.
Using provided config file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json
[2025-05-25 16:12:28] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 16:12:28] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 107691520 bytes)
[2025-05-25 16:12:28] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 16:12:30] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 16:12:30] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52726784 bytes)
[2025-05-25 16:12:30] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 16:12:31] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
PS>TerminatingError(Merge-EnvVars): "Cannot process argument transformation on parameter 'overrideMap'. Cannot convert value "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" to type "System.Collections.Hashtable". Error: "Cannot convert the "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" value of type "System.Management.Automation.PSCustomObject" to type "System.Collections.Hashtable".""
WARNING: Failed to parse config file. Using default values.
[2025-05-25 16:12:31] [ERROR] Error parsing config file: Cannot process argument transformation on parameter 'overrideMap'. Cannot convert value "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" to type "System.Collections.Hashtable". Error: "Cannot convert the "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" value of type "System.Management.Automation.PSCustomObject" to type "System.Collections.Hashtable"."
[2025-05-25 16:12:31] [INFO] Checking if eco-edge-network exists...
[2025-05-25 16:12:32] [INFO] eco-edge-network already exists
[2025-05-25 16:12:32] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER b6856a0b93cb  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 16:12:32] [INFO] Checking for existing containers...
[2025-05-25 16:12:32] [INFO] Found existing container: octopus-service
[2025-05-25 16:12:33] [INFO] Stopping container: octopus-service
[2025-05-25 16:12:37] [INFO] Stop container output: octopus-service
[2025-05-25 16:12:37] [INFO] Removing container: octopus-service
[2025-05-25 16:12:38] [INFO] Remove container output: octopus-service
[2025-05-25 16:12:38] [INFO] Found existing container: ewp-edge
[2025-05-25 16:12:38] [INFO] Stopping container: ewp-edge
[2025-05-25 16:12:40] [INFO] Stop container output: ewp-edge
[2025-05-25 16:12:40] [INFO] Removing container: ewp-edge
[2025-05-25 16:12:40] [INFO] Remove container output: ewp-edge
[2025-05-25 16:12:40] [INFO] Running octopus-service container...
[2025-05-25 16:12:40] [INFO] Added backend environment arguments: -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 16:12:40] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem --user 1000:1000 octopus-service:latest
[2025-05-25 16:12:42] [INFO] Container start output: 88d2185d6139f40d25021f9827975745a3b49426a7752f4cf76de3bc8f0197a2
[2025-05-25 16:12:45] [INFO] octopus-service container started successfully
[2025-05-25 16:12:45] [INFO] Running ewp-edge container...
[2025-05-25 16:12:45] [INFO] Added frontend environment arguments: -e NGINX_PORT=80 -e API_URL=http://localhost:9080
[2025-05-25 16:12:45] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -e NGINX_PORT=80 -e API_URL=http://localhost:9080 ewp-edge:latest
[2025-05-25 16:12:46] [INFO] Container start output: 3cce5722902949b7769245a184654e45e40be5eca96008b0f1f9a48a78fac971
[2025-05-25 16:12:49] [INFO] ewp-edge container started successfully
[2025-05-25 16:12:49] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 16:12:49] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 16:12:49] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 16:12:49] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 16:12:49] [INFO] Found 30 files in source directory
[2025-05-25 16:12:49] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
PS>TerminatingError(Copy-Item): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'."
[2025-05-25 16:12:49] [ERROR] Error copying files: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'.
[2025-05-25 16:12:49] [INFO] Setting up startup shortcut...
[2025-05-25 16:12:49] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 16:12:49] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 16:12:49] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 16:12:49] [INFO] Monitor executable found, creating shortcut
[2025-05-25 16:12:49] [INFO] Startup shortcut created successfully
[2025-05-25 16:12:49] [INFO] Found existing Eco Edge Monitor processes, stopping them
[2025-05-25 16:12:49] [INFO] Process with ID 33296 stopped
[2025-05-25 16:12:49] [INFO] Stopped existing Eco Edge Monitor instances.
[2025-05-25 16:12:50] [INFO] Starting Eco Edge Monitor application
[2025-05-25 16:12:50] [INFO] Started Eco Edge Monitor with process ID: 3276
[2025-05-25 16:12:50] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 16:12:52] [INFO] Verified Eco Edge Monitor is running with process ID: 3276
[2025-05-25 16:12:52] [INFO] Eco Edge Agent installation complete.
**********************
PowerShell transcript end
End time: 20250525161252
**********************
