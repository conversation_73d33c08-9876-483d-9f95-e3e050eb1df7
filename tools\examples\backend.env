# Backend environment configuration
REST_PORT=9080
REST_HOST=0.0.0.0
CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem
CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem
CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem
CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt
CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url
DB_PATH=/app/db
AUTH_DB_PATH=/app/db/auth/auth.db
AUTH_CONFIG_DB_PATH=/app/db/auth/config.db
SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db
CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db
