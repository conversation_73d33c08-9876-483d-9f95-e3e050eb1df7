function Get-PackageParameters {
    [CmdletBinding()]
    param(
        [parameter(Mandatory = $false, Position = 0)]
        [string] $Parameters = $env:ChocolateyPackageParameters,
        [parameter(Mandatory = $false, Position = 1)]
        [hashtable] $DefaultValues = @{}
    )

    $arguments = @{}

    if ($Parameters) {
        $match_pattern = "\/([a-zA-Z0-9]+):([`"'])?([a-zA-Z0-9- _\\:\.]+)([`"'])?"
        $pattern = New-Object System.Text.RegularExpressions.Regex($match_pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        $matches = $pattern.Matches($Parameters)
        
        if ($matches -and $matches.Count -gt 0) {
            $matches | ForEach-Object {
                $arguments.Add($_.Groups[1].Value.Trim(), $_.Groups[3].Value.Trim())
            }
        }
    }

    # Merge with default values
    foreach ($key in $DefaultValues.Keys) {
        if (-not $arguments.ContainsKey($key)) {
            $arguments.Add($key, $DefaultValues[$key])
        }
    }

    return $arguments
}

function Test-PortInUse {
    param(
        [Parameter(Mandatory = $true)]
        [int]$Port
    )

    $tcpClient = New-Object System.Net.Sockets.TcpClient
    try {
        $tcpClient.Connect("127.0.0.1", $Port)
        $tcpClient.Close()
        return $true
    } catch {
        return $false
    }
}

function Find-AvailablePort {
    param(
        [Parameter(Mandatory = $true)]
        [int]$StartPort,
        [Parameter(Mandatory = $false)]
        [int]$EndPort = $StartPort + 100
    )

    for ($port = $StartPort; $port -le $EndPort; $port++) {
        if (-not (Test-PortInUse -Port $port)) {
            return $port
        }
    }

    throw "No available ports found in range $StartPort-$EndPort"
}
