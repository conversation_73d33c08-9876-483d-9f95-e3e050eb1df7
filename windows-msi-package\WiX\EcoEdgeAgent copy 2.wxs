<?xml version="1.0" encoding="utf-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs"
     xmlns:ui="http://wixtoolset.org/schemas/v4/wxs/ui">
  <Package Name="Eco Edge Agent" 
           Manufacturer="YourCompany"
           Version="1.0.0.0"
           UpgradeCode="D2B5C5A2-7F7B-4E3B-8D0A-1F8B6B2D2C1E">
    
    <!-- Define properties -->
    <Property Id="CONFIGFILEPATH" Secure="yes" />
    <Property Id="WIXUI_INSTALLDIR" Value="INSTALLFOLDER" />
    
    <!-- Define directory structure -->
    <StandardDirectory Id="ProgramFilesFolder">
      <Directory Id="INSTALLFOLDER" Name="EcoEdgeAgent" />
    </StandardDirectory>

    <!-- Define components -->
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component>
        <File Source="..\scripts\install.ps1" Id="InstallPs1" />
      </Component>
    </ComponentGroup>

    <!-- Define features -->
    <Feature Id="ProductFeature" Title="Eco Edge Agent" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
    </Feature>

    <!-- Use built-in WixUI -->
    <ui:WixUI Id="WixUI_InstallDir" />
    
    <!-- Custom UI for config file selection -->
    <UI>
      <!-- Custom dialog for config file selection -->
      <Dialog Id="ConfigFileDlg" Width="370" Height="270" Title="Configuration File">
        <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes">
          <Text Value="Configuration File Selection" />
        </Control>
        
        <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes">
          <Text Value="Select a configuration file (optional):" />
        </Control>
        
        <!-- Config file path text box -->
        <Control Id="ConfigFilePathEdit" Type="Edit" X="25" Y="50" Width="250" Height="18" Property="CONFIGFILEPATH" />
        
        <!-- Browse button -->
        <Control Id="BrowseButton" Type="PushButton" X="280" Y="49" Width="56" Height="20">
          <Text Value="Browse..." />
        </Control>
        
        <!-- Info text -->
        <Control Id="InfoText" Type="Text" X="25" Y="80" Width="320" Height="40" Transparent="yes" NoPrefix="yes">
          <Text Value="Leave empty to use default configuration. The selected file will be passed to the installation script." />
        </Control>
        
        <!-- Navigation buttons -->
        <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17">
          <Text Value="&amp;Back" />
        </Control>
        <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes">
          <Text Value="&amp;Next" />
        </Control>
        <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes">
          <Text Value="Cancel" />
        </Control>
        
        <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="370" Height="0" />
        <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="370" Height="0" />
      </Dialog>
      
      <!-- Navigation events -->
      <Publish Dialog="InstallDirDlg" Control="Next" Event="NewDialog" Value="ConfigFileDlg" Order="2" />
      <Publish Dialog="VerifyReadyDlg" Control="Back" Event="NewDialog" Value="ConfigFileDlg" Order="2" />
      
      <Publish Dialog="ConfigFileDlg" Control="Back" Event="NewDialog" Value="InstallDirDlg" />
      <Publish Dialog="ConfigFileDlg" Control="Next" Event="NewDialog" Value="VerifyReadyDlg" />
      <Publish Dialog="ConfigFileDlg" Control="Cancel" Event="SpawnDialog" Value="CancelDlg" />
      
      <!-- Simple browse functionality using DirectoryList -->
      <Publish Dialog="ConfigFileDlg" Control="BrowseButton" Event="SpawnDialog" Value="BrowseDlg" />
    </UI>
    
    <!-- Custom actions for PowerShell execution -->
    <CustomAction Id="RunInstallScript"
                  Directory="INSTALLFOLDER"
                  ExeCommand='powershell.exe -ExecutionPolicy Bypass -File "[INSTALLFOLDER]install.ps1" -ConfigFilePath "[CONFIGFILEPATH]"'
                  Execute="deferred"
                  Return="check"
                  Impersonate="no" />
    
    <CustomAction Id="RunInstallScriptNoConfig"
                  Directory="INSTALLFOLDER"
                  ExeCommand='powershell.exe -ExecutionPolicy Bypass -File "[INSTALLFOLDER]install.ps1"'
                  Execute="deferred"
                  Return="check"
                  Impersonate="no" />
                  
    <InstallExecuteSequence>
      <Custom Action="RunInstallScript" After="InstallFiles" Condition='NOT Installed AND CONFIGFILEPATH &lt;&gt; ""' />
      <Custom Action="RunInstallScriptNoConfig" After="InstallFiles" Condition='NOT Installed AND (CONFIGFILEPATH = "" OR NOT CONFIGFILEPATH)' />
    </InstallExecuteSequence>
  </Package>
</Wix>