# Build script for Eco Edge Monitor Flutter application

# Ensure we're in the correct directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
Set-Location $scriptPath

# Get Flutter path
$flutterPath = "C:\tools\flutter\bin\flutter.bat"

# Check if Flutter is available
if (-not (Test-Path $flutterPath)) {
    Write-Error "Flutter not found at $flutterPath. Please install Flutter or update the path in this script."
    exit 1
}

# Clean the project
Write-Host "Cleaning the project..."
& $flutterPath clean

# Get dependencies
Write-Host "Getting dependencies..."
& $flutterPath pub get

# Build Windows application
Write-Host "Building Windows application..."
& $flutterPath build windows --release

# Check if build was successful
if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed with exit code $LASTEXITCODE"
    exit $LASTEXITCODE
}

Write-Host "Build completed successfully!"
Write-Host "The application is available at: $scriptPath\build\windows\runner\Release\"

# Create output directory
$outputDir = "$scriptPath\..\tools\chocolateyinstall\eco-edge-monitor"
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
}

#Create output directory for msi package
$outputDirMsi = "$scriptPath\..\msi-package\eco-edge"
if (-not (Test-Path $outputDirMsi)) {
    New-Item -ItemType Directory -Path $outputDirMsi -Force | Out-Null
}

# Copy the built application to the output directory
Write-Host "Copying application to Chocolatey package directory..."
Copy-Item -Path "$scriptPath\build\windows\x64\runner\Release\*" -Destination $outputDir -Recurse -Force

# Copy the built application to the output directory for msi package
Write-Host "Copying application to MSI package directory..."
Copy-Item -Path "$scriptPath\build\windows\x64\runner\Release\*" -Destination $outputDirMsi -Recurse -Force

Write-Host "Application has been built and copied to the Chocolatey and MSI package directory."
