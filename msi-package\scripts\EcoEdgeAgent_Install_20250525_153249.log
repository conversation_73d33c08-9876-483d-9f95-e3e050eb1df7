**********************
PowerShell transcript start
Start time: 20250525153249
Username: APA\SESA808825
RunAs User: APA\SESA808825
Configuration Name: 
Machine: WIN01502063L (Microsoft Windows NT 10.0.22631.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -noexit -command try { . "c:\Users\<USER>\AppData\Local\Programs\Windsurf\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch {}
Process ID: 42304
PSVersion: 7.5.0
PSEdition: Core
GitCommitId: 7.5.0
OS: Microsoft Windows 10.0.22631
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_153249.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_153249.log
[2025-05-25 15:32:49] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 15:32:49] [INFO] Checking for Podman installation...
[2025-05-25 15:32:49] [WARNING] Podman not found. Installing Podman...
[2025-05-25 15:32:49] [INFO] Downloading Podman installer from https://github.com/containers/podman/releases/download/v5.5.0/podman-5.5.0-setup.exe ...
[2025-05-25 15:32:49] [INFO] Installer will be saved to: C:\Users\<USER>\AppData\Local\Temp\podman-setup.exe
[2025-05-25 15:32:54] [INFO] Download completed. File size: 29828432 bytes
[2025-05-25 15:32:54] [INFO] Running Podman installer...
[2025-05-25 15:33:18] [INFO] Installer process exit code: 1618
[2025-05-25 15:33:18] [WARNING] Installer returned non-zero exit code: 1618
[2025-05-25 15:33:18] [INFO] Podman installation completed.
[2025-05-25 15:33:18] [INFO] Refreshing PATH environment variable...
[2025-05-25 15:33:18] [INFO] PATH refreshed successfully.
[2025-05-25 15:33:18] [INFO] Current PATH: C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\Windows Imaging\;C:\Program Files\CMake\bin;C:\Program Files\Java\jre1.8.0_441\bin;C:\Program Files\Graphviz\bin;C:\Program Files\PowerShell\7\;C:\Program Files\dotnet\;C:\Program Files\Go\bin;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\1E\Client\Extensibility\NomadBranch;C:\Program Files\WiX Toolset v5.0\bin\;
[2025-05-25 15:33:18] [INFO] Verifying Podman installation...
[2025-05-25 15:33:18] [ERROR] Podman installation failed or podman not found in PATH.
]633;D;1]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\scripts\install.ps1
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_153755.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_153755.log
[2025-05-25 15:37:55] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 15:37:55] [INFO] Checking for Podman installation...
[2025-05-25 15:37:55] [WARNING] Podman not found. Installing Podman...
[2025-05-25 15:37:55] [INFO] Downloading Podman installer from https://github.com/containers/podman/releases/download/v5.5.0/podman-5.5.0-setup.exe ...
[2025-05-25 15:37:55] [INFO] Installer will be saved to: C:\Users\<USER>\AppData\Local\Temp\podman-setup.exe
[2025-05-25 15:38:01] [INFO] Download completed. File size: 29828432 bytes
[2025-05-25 15:38:01] [INFO] Running Podman installer...
[2025-05-25 15:38:09] [INFO] Installer process exit code: 0
[2025-05-25 15:38:09] [INFO] Podman installer completed successfully.
[2025-05-25 15:38:09] [INFO] Podman installation completed.
[2025-05-25 15:38:09] [INFO] Refreshing PATH environment variable...
[2025-05-25 15:38:09] [INFO] PATH refreshed successfully.
[2025-05-25 15:38:09] [INFO] Current PATH: C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\Windows Imaging\;C:\Program Files\CMake\bin;C:\Program Files\Java\jre1.8.0_441\bin;C:\Program Files\Graphviz\bin;C:\Program Files\PowerShell\7\;C:\Program Files\dotnet\;C:\Program Files\Go\bin;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\1E\Client\Extensibility\NomadBranch;C:\Program Files\WiX Toolset v5.0\bin\;C:\Program Files\RedHat\Podman\
[2025-05-25 15:38:09] [INFO] Verifying Podman installation...
Cannot connect to Podman. Please verify your connection to the Linux system using `podman system connection list`, or try `podman machine init` and `podman machine start` to manage a new Linux VM
Error: unable to connect to Podman socket: failed to connect: dial tcp 127.0.0.1:56902: connectex: No connection could be made because the target machine actively refused it.
[2025-05-25 15:38:10] [INFO] Podman installed successfully. Version:
[2025-05-25 15:38:10] [INFO] Checking for existing Podman machine...
[2025-05-25 15:38:11] [INFO] Podman machine already exists. Checking if it's running...
[2025-05-25 15:38:11] [INFO] Starting existing Podman machine...
[2025-05-25 15:38:21] [INFO] Machine start output: Starting machine "podman-machine-default" your 131072x1 screen size is bogus. expect trouble API forwarding listening on: npipe:////./pipe/docker_engine  Docker API clients default to this address. You do not need to set DOCKER_HOST. Machine "podman-machine-default" started successfully
[2025-05-25 15:38:21] [INFO] Testing Podman connection...
[2025-05-25 15:38:22] [INFO] Podman connection test completed successfully
WARNING: No config file found! Using default values.
[2025-05-25 15:38:22] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 15:38:22] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 107691520 bytes)
[2025-05-25 15:38:22] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 15:38:24] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 15:38:24] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52726784 bytes)
[2025-05-25 15:38:24] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 15:38:25] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
Created data directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\data
[2025-05-25 15:38:26] [INFO] Checking if eco-edge-network exists...
[2025-05-25 15:38:26] [INFO] eco-edge-network already exists
[2025-05-25 15:38:26] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER a62bbe9be120  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 15:38:26] [INFO] Checking for existing containers...
[2025-05-25 15:38:27] [INFO] Found existing container: octopus-service
[2025-05-25 15:38:27] [INFO] Removing container: octopus-service
[2025-05-25 15:38:27] [INFO] Remove container output: octopus-service
[2025-05-25 15:38:28] [INFO] Found existing container: ewp-edge
[2025-05-25 15:38:28] [INFO] Removing container: ewp-edge
[2025-05-25 15:38:29] [INFO] Remove container output: ewp-edge
[2025-05-25 15:38:29] [INFO] Running octopus-service container...
[2025-05-25 15:38:29] [INFO] Added backend environment arguments: -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 15:38:29] [INFO] Added volume arguments: -v C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\data:/app/data
[2025-05-25 15:38:29] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -v C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\data:/app/data --user 1000:1000 octopus-service:latest
[2025-05-25 15:38:29] [INFO] Container start output: cdfa7995d931c836fa4382296838091daa840274e6499b44ab264102350542ec
[2025-05-25 15:38:32] [INFO] octopus-service container started successfully
[2025-05-25 15:38:32] [INFO] Running ewp-edge container...
[2025-05-25 15:38:32] [INFO] Added frontend environment arguments: -e NGINX_PORT=80 -e API_URL=http://localhost:9080
[2025-05-25 15:38:32] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -e NGINX_PORT=80 -e API_URL=http://localhost:9080 ewp-edge:latest
[2025-05-25 15:38:32] [INFO] Container start output: bf2a0aa92f0cf25ef0e317d4d31ad80cc2743e37119c84d5a4587f174c30176f
[2025-05-25 15:38:35] [INFO] ewp-edge container started successfully
[2025-05-25 15:38:35] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 15:38:35] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 15:38:35] [INFO] Created directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 15:38:35] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 15:38:35] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 15:38:35] [INFO] Found 30 files in source directory
[2025-05-25 15:38:35] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 15:38:35] [INFO] Files copied successfully
[2025-05-25 15:38:35] [INFO] Setting up startup shortcut...
[2025-05-25 15:38:35] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 15:38:35] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 15:38:35] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 15:38:35] [INFO] Monitor executable found, creating shortcut
[2025-05-25 15:38:35] [INFO] Startup shortcut created successfully
[2025-05-25 15:38:35] [INFO] No existing Eco Edge Monitor processes found
[2025-05-25 15:38:35] [INFO] Starting Eco Edge Monitor application
[2025-05-25 15:38:35] [INFO] Started Eco Edge Monitor with process ID: 36424
[2025-05-25 15:38:35] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 15:38:37] [INFO] Verified Eco Edge Monitor is running with process ID: 36424
[2025-05-25 15:38:37] [INFO] Eco Edge Agent installation complete.
Transcript stopped, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_153755.log
Installation log saved to: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_153755.log
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman ps

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman container stop cd bf
cd
bf
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman ps

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman ps

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman ps

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman ps

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman ps

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman ps

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman ps

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman ps

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman container rm cd bf

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman ps -a

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman images 

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman image rm 39 8d

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman system connection list

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman system connection rm podman-machine-default

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman system connection list

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman system connection rm podman-machine-default-root

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman system connection list

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman machine --help

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman machine list

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman machine rm 

]633;D;1]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman machine stop

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman machine rm 

]633;D;1]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman machine rest

]633;D;1]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman machine reset

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\scripts\install.ps1
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_154853.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_154853.log
[2025-05-25 15:48:53] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 15:48:53] [INFO] Checking for Podman installation...
[2025-05-25 15:48:53] [WARNING] Podman not found. Installing Podman...
[2025-05-25 15:48:53] [INFO] Downloading Podman installer from https://github.com/containers/podman/releases/download/v5.5.0/podman-5.5.0-setup.exe ...
[2025-05-25 15:48:53] [INFO] Installer will be saved to: C:\Users\<USER>\AppData\Local\Temp\podman-setup.exe
[2025-05-25 15:48:58] [INFO] Download completed. File size: 29828432 bytes
[2025-05-25 15:48:58] [INFO] Running Podman installer...
[2025-05-25 15:49:06] [INFO] Installer process exit code: 0
[2025-05-25 15:49:06] [INFO] Podman installer completed successfully.
[2025-05-25 15:49:06] [INFO] Podman installation completed.
[2025-05-25 15:49:06] [INFO] Refreshing PATH environment variable...
[2025-05-25 15:49:06] [INFO] PATH refreshed successfully.
[2025-05-25 15:49:06] [INFO] Current PATH: C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\Windows Imaging\;C:\Program Files\CMake\bin;C:\Program Files\Java\jre1.8.0_441\bin;C:\Program Files\Graphviz\bin;C:\Program Files\PowerShell\7\;C:\Program Files\dotnet\;C:\Program Files\Go\bin;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313;C:\Program Files\1E\Client\Extensibility\NomadBranch;C:\Program Files\WiX Toolset v5.0\bin\;C:\Program Files\RedHat\Podman\
[2025-05-25 15:49:06] [INFO] Verifying Podman installation...
Cannot connect to Podman. Please verify your connection to the Linux system using `podman system connection list`, or try `podman machine init` and `podman machine start` to manage a new Linux VM
Error: unable to connect to Podman socket: Get "http://d/v5.5.0/libpod/_ping": dial unix /run/podman/podman.sock: connect: A socket operation encountered a dead network.
[2025-05-25 15:49:08] [INFO] Podman installed successfully. Version:
[2025-05-25 15:49:08] [INFO] Checking for existing Podman machine...
[2025-05-25 15:49:08] [INFO] Creating new Podman machine with WSL...
[2025-05-25 15:51:24] [INFO] Machine initialization output: Looking up Podman Machine image at quay.io/podman/machine-os-wsl:5.5 to create VM Getting image source signatures Copying blob sha256:36dfbab0081f18cc0d057773b87e2751fdb8c72b066d1efa0dc5e97f5136b907 Copying config sha256:44136fa355b3678a1146ad16f7e8649e94fb4fc21fe77e8310c060f61caaff8a Writing manifest to image destination 36dfbab0081f18cc0d057773b87e2751fdb8c72b066d1efa0dc5e97f5136b907 Importing operating system into WSL (this may take a few minutes on a new WSL install)... T h e   o p e r a t i o n   c o m p l e t e d   s u c c e s s f u l l y .       Configuring system... Machine init complete Starting machine "podman-machine-default" your 131072x1 screen size is bogus. expect trouble API forwarding listening on: npipe:////./pipe/docker_engine  Docker API clients default to this address. You do not need to set DOCKER_HOST. Machine "podman-machine-default" started successfully
[2025-05-25 15:51:24] [INFO] Machine verification: NAME                    VM TYPE     CREATED        LAST UP            CPUS        MEMORY      DISK SIZE podman-machine-default  wsl         2 minutes ago  Currently running  2           2GiB        20GiB
[2025-05-25 15:51:24] [INFO] Testing Podman connection...
[2025-05-25 15:51:25] [INFO] Podman connection test completed successfully
WARNING: No config file found! Using default values.
[2025-05-25 15:51:25] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 15:51:25] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 107691520 bytes)
[2025-05-25 15:51:25] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 15:51:35] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 15:51:35] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52726784 bytes)
[2025-05-25 15:51:35] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 15:51:37] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
[2025-05-25 15:51:37] [INFO] Checking if eco-edge-network exists...
[2025-05-25 15:51:38] [INFO] Creating eco-edge-network...
[2025-05-25 15:51:38] [INFO] Network creation output: eco-edge-network
[2025-05-25 15:51:39] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER b6856a0b93cb  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 15:51:39] [INFO] Checking for existing containers...
[2025-05-25 15:51:39] [INFO] Container octopus-service does not exist, no need to remove
[2025-05-25 15:51:39] [INFO] Container ewp-edge does not exist, no need to remove
[2025-05-25 15:51:39] [INFO] Running octopus-service container...
[2025-05-25 15:51:39] [INFO] Added backend environment arguments: -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 15:51:39] [INFO] Added volume arguments: -v C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\data:/app/data
[2025-05-25 15:51:39] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -v C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\data:/app/data --user 1000:1000 octopus-service:latest
[2025-05-25 15:51:41] [INFO] Container start output: dbe910fafe917bb48fc43b329b06b671d3121d6f94770de6aa5e1049e5f7ec14
[2025-05-25 15:51:44] [INFO] octopus-service container started successfully
[2025-05-25 15:51:44] [INFO] Running ewp-edge container...
[2025-05-25 15:51:44] [INFO] Added frontend environment arguments: -e NGINX_PORT=80 -e API_URL=http://localhost:9080
[2025-05-25 15:51:44] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -e NGINX_PORT=80 -e API_URL=http://localhost:9080 ewp-edge:latest
[2025-05-25 15:51:45] [INFO] Container start output: 7ee9663f4b49081a316bf5ec9dafd6f2103c7589c2c7b773f04d2bd445479c0a
[2025-05-25 15:51:48] [INFO] ewp-edge container started successfully
[2025-05-25 15:51:48] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 15:51:48] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 15:51:48] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 15:51:48] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 15:51:48] [INFO] Found 30 files in source directory
[2025-05-25 15:51:48] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 15:51:48] [INFO] Files copied successfully
[2025-05-25 15:51:48] [INFO] Setting up startup shortcut...
[2025-05-25 15:51:48] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 15:51:48] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 15:51:48] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 15:51:48] [INFO] Monitor executable found, creating shortcut
[2025-05-25 15:51:48] [INFO] Startup shortcut created successfully
[2025-05-25 15:51:48] [INFO] No existing Eco Edge Monitor processes found
[2025-05-25 15:51:48] [INFO] Starting Eco Edge Monitor application
[2025-05-25 15:51:48] [INFO] Started Eco Edge Monitor with process ID: 39984
[2025-05-25 15:51:48] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 15:51:50] [INFO] Verified Eco Edge Monitor is running with process ID: 39984
[2025-05-25 15:51:50] [INFO] Eco Edge Agent installation complete.
Transcript stopped, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_154853.log
Installation log saved to: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_154853.log
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>podman ps

]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\scripts\install.ps1 -ConfigFilePath "C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json"
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_160146.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_160146.log
[2025-05-25 16:01:46] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 16:01:46] [INFO] Checking for Podman installation...
Podman is already installed.
Using provided config file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json
[2025-05-25 16:01:46] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 16:01:46] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 107691520 bytes)
[2025-05-25 16:01:46] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 16:01:48] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 16:01:48] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52726784 bytes)
[2025-05-25 16:01:48] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 16:01:50] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
PS>TerminatingError(Merge-EnvVars): "Cannot process argument transformation on parameter 'overrideMap'. Cannot convert value "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" to type "System.Collections.Hashtable". Error: "Cannot convert the "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" value of type "System.Management.Automation.PSCustomObject" to type "System.Collections.Hashtable".""
WARNING: Failed to parse config file. Using default values.
[2025-05-25 16:01:50] [INFO] Checking if eco-edge-network exists...
[2025-05-25 16:01:50] [INFO] eco-edge-network already exists
[2025-05-25 16:01:50] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER b6856a0b93cb  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 16:01:50] [INFO] Checking for existing containers...
[2025-05-25 16:01:51] [INFO] Found existing container: octopus-service
[2025-05-25 16:01:51] [INFO] Stopping container: octopus-service
[2025-05-25 16:01:55] [INFO] Stop container output: octopus-service
[2025-05-25 16:01:55] [INFO] Removing container: octopus-service
[2025-05-25 16:01:55] [INFO] Remove container output: octopus-service
[2025-05-25 16:01:55] [INFO] Found existing container: ewp-edge
[2025-05-25 16:01:56] [INFO] Stopping container: ewp-edge
[2025-05-25 16:01:57] [INFO] Stop container output: ewp-edge
[2025-05-25 16:01:57] [INFO] Removing container: ewp-edge
[2025-05-25 16:01:58] [INFO] Remove container output: ewp-edge
[2025-05-25 16:01:58] [INFO] Running octopus-service container...
[2025-05-25 16:01:58] [INFO] Added backend environment arguments: -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 16:01:58] [INFO] Added volume arguments: -v C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\data:/app/data
[2025-05-25 16:01:58] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -v C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\data:/app/data --user 1000:1000 octopus-service:latest
[2025-05-25 16:02:00] [INFO] Container start output: be486213e2d86dda913ec53d49340bb0bd125590cf5d8c071d5b41562156bded
[2025-05-25 16:02:02] [INFO] octopus-service container started successfully
[2025-05-25 16:02:02] [INFO] Running ewp-edge container...
[2025-05-25 16:02:02] [INFO] Added frontend environment arguments: -e NGINX_PORT=80 -e API_URL=http://localhost:9080
[2025-05-25 16:02:02] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -e NGINX_PORT=80 -e API_URL=http://localhost:9080 ewp-edge:latest
[2025-05-25 16:02:04] [INFO] Container start output: 22cbcc7d9f9326abbb9cd98d0b3ae61e3ee90d75fff4a103162c7aa86c574b62
[2025-05-25 16:02:06] [INFO] ewp-edge container started successfully
[2025-05-25 16:02:06] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 16:02:06] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 16:02:06] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 16:02:06] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 16:02:06] [INFO] Found 30 files in source directory
[2025-05-25 16:02:06] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
PS>TerminatingError(Copy-Item): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'."
[2025-05-25 16:02:06] [ERROR] Error copying files: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'.
[2025-05-25 16:02:06] [INFO] Setting up startup shortcut...
[2025-05-25 16:02:06] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 16:02:06] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 16:02:06] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 16:02:06] [INFO] Monitor executable found, creating shortcut
[2025-05-25 16:02:06] [INFO] Startup shortcut created successfully
[2025-05-25 16:02:06] [INFO] Found existing Eco Edge Monitor processes, stopping them
[2025-05-25 16:02:06] [INFO] Process with ID 39984 stopped
[2025-05-25 16:02:06] [INFO] Stopped existing Eco Edge Monitor instances.
[2025-05-25 16:02:07] [INFO] Starting Eco Edge Monitor application
[2025-05-25 16:02:08] [INFO] Started Eco Edge Monitor with process ID: 21312
[2025-05-25 16:02:08] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 16:02:10] [INFO] Verified Eco Edge Monitor is running with process ID: 21312
[2025-05-25 16:02:10] [INFO] Eco Edge Agent installation complete.
Transcript stopped, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_160146.log
Installation log saved to: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_160146.log
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\scripts\install.ps1 -ConfigFilePath "C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json"
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_160831.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_160831.log
[2025-05-25 16:08:31] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 16:08:31] [INFO] Checking for Podman installation...
Podman is already installed.
Using provided config file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json
[2025-05-25 16:08:31] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 16:08:31] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 107691520 bytes)
[2025-05-25 16:08:31] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 16:08:33] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 16:08:33] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52726784 bytes)
[2025-05-25 16:08:33] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 16:08:35] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
PS>TerminatingError(Merge-EnvVars): "Cannot process argument transformation on parameter 'overrideMap'. Cannot convert value "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" to type "System.Collections.Hashtable". Error: "Cannot convert the "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" value of type "System.Management.Automation.PSCustomObject" to type "System.Collections.Hashtable".""
WARNING: Failed to parse config file. Using default values.
[2025-05-25 16:08:35] [INFO] Checking if eco-edge-network exists...
[2025-05-25 16:08:35] [INFO] eco-edge-network already exists
[2025-05-25 16:08:35] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER b6856a0b93cb  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 16:08:35] [INFO] Checking for existing containers...
[2025-05-25 16:08:36] [INFO] Found existing container: octopus-service
[2025-05-25 16:08:36] [INFO] Stopping container: octopus-service
[2025-05-25 16:08:43] [INFO] Stop container output: octopus-service
[2025-05-25 16:08:43] [INFO] Removing container: octopus-service
[2025-05-25 16:08:43] [INFO] Remove container output: octopus-service
[2025-05-25 16:08:44] [INFO] Found existing container: ewp-edge
[2025-05-25 16:08:44] [INFO] Stopping container: ewp-edge
[2025-05-25 16:08:46] [INFO] Stop container output: ewp-edge
[2025-05-25 16:08:46] [INFO] Removing container: ewp-edge
[2025-05-25 16:08:46] [INFO] Remove container output: ewp-edge
[2025-05-25 16:08:46] [INFO] Running octopus-service container...
[2025-05-25 16:08:46] [INFO] Added backend environment arguments: -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 16:08:46] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem --user 1000:1000 octopus-service:latest
[2025-05-25 16:08:48] [INFO] Container start output: c81a2c67cb9030820796c39ff1a231be6eb3742c5db3afb703cb7782736b1444
[2025-05-25 16:08:50] [INFO] octopus-service container started successfully
[2025-05-25 16:08:50] [INFO] Running ewp-edge container...
[2025-05-25 16:08:50] [INFO] Added frontend environment arguments: -e NGINX_PORT=80 -e API_URL=http://localhost:9080
[2025-05-25 16:08:50] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -e NGINX_PORT=80 -e API_URL=http://localhost:9080 ewp-edge:latest
[2025-05-25 16:08:52] [INFO] Container start output: 466d663a4820e66d1dd6b18605064a88f4efb078e9b6ef91b9bb533a51b8de05
[2025-05-25 16:08:55] [INFO] ewp-edge container started successfully
[2025-05-25 16:08:55] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 16:08:55] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 16:08:55] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 16:08:55] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 16:08:55] [INFO] Found 30 files in source directory
[2025-05-25 16:08:55] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
PS>TerminatingError(Copy-Item): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'."
[2025-05-25 16:08:55] [ERROR] Error copying files: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'.
[2025-05-25 16:08:55] [INFO] Setting up startup shortcut...
[2025-05-25 16:08:55] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 16:08:55] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 16:08:55] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 16:08:55] [INFO] Monitor executable found, creating shortcut
[2025-05-25 16:08:55] [INFO] Startup shortcut created successfully
[2025-05-25 16:08:55] [INFO] Found existing Eco Edge Monitor processes, stopping them
[2025-05-25 16:08:55] [INFO] Process with ID 21312 stopped
[2025-05-25 16:08:55] [INFO] Stopped existing Eco Edge Monitor instances.
[2025-05-25 16:08:56] [INFO] Starting Eco Edge Monitor application
[2025-05-25 16:08:56] [INFO] Started Eco Edge Monitor with process ID: 33296
[2025-05-25 16:08:56] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 16:08:58] [INFO] Verified Eco Edge Monitor is running with process ID: 33296
[2025-05-25 16:08:58] [INFO] Eco Edge Agent installation complete.
Transcript stopped, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_160831.log
Installation log saved to: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_160831.log
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\scripts\install.ps1 -ConfigFilePath "C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json"
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161228.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161228.log
[2025-05-25 16:12:28] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 16:12:28] [INFO] Checking for Podman installation...
Podman is already installed.
Using provided config file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json
[2025-05-25 16:12:28] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 16:12:28] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 107691520 bytes)
[2025-05-25 16:12:28] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 16:12:30] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 16:12:30] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52726784 bytes)
[2025-05-25 16:12:30] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 16:12:31] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
PS>TerminatingError(Merge-EnvVars): "Cannot process argument transformation on parameter 'overrideMap'. Cannot convert value "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" to type "System.Collections.Hashtable". Error: "Cannot convert the "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" value of type "System.Management.Automation.PSCustomObject" to type "System.Collections.Hashtable".""
WARNING: Failed to parse config file. Using default values.
[2025-05-25 16:12:31] [ERROR] Error parsing config file: Cannot process argument transformation on parameter 'overrideMap'. Cannot convert value "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" to type "System.Collections.Hashtable". Error: "Cannot convert the "@{REST_PORT=9080; REST_HOST=0.0.0.0; CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem; CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem; CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem; CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem; CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt; CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com; DB_PATH=/app/db; AUTH_DB_PATH=/app/db/auth/auth.db; AUTH_CONFIG_DB_PATH=/app/db/auth/config.db; SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db; CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db}" value of type "System.Management.Automation.PSCustomObject" to type "System.Collections.Hashtable"."
[2025-05-25 16:12:31] [INFO] Checking if eco-edge-network exists...
[2025-05-25 16:12:32] [INFO] eco-edge-network already exists
[2025-05-25 16:12:32] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER b6856a0b93cb  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 16:12:32] [INFO] Checking for existing containers...
[2025-05-25 16:12:32] [INFO] Found existing container: octopus-service
[2025-05-25 16:12:33] [INFO] Stopping container: octopus-service
[2025-05-25 16:12:37] [INFO] Stop container output: octopus-service
[2025-05-25 16:12:37] [INFO] Removing container: octopus-service
[2025-05-25 16:12:38] [INFO] Remove container output: octopus-service
[2025-05-25 16:12:38] [INFO] Found existing container: ewp-edge
[2025-05-25 16:12:38] [INFO] Stopping container: ewp-edge
[2025-05-25 16:12:40] [INFO] Stop container output: ewp-edge
[2025-05-25 16:12:40] [INFO] Removing container: ewp-edge
[2025-05-25 16:12:40] [INFO] Remove container output: ewp-edge
[2025-05-25 16:12:40] [INFO] Running octopus-service container...
[2025-05-25 16:12:40] [INFO] Added backend environment arguments: -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 16:12:40] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_BOOTSTRAP_SERVER_URL=boot-strap-url -e REST_PORT=9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_HOST=0.0.0.0 -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem --user 1000:1000 octopus-service:latest
[2025-05-25 16:12:42] [INFO] Container start output: 88d2185d6139f40d25021f9827975745a3b49426a7752f4cf76de3bc8f0197a2
[2025-05-25 16:12:45] [INFO] octopus-service container started successfully
[2025-05-25 16:12:45] [INFO] Running ewp-edge container...
[2025-05-25 16:12:45] [INFO] Added frontend environment arguments: -e NGINX_PORT=80 -e API_URL=http://localhost:9080
[2025-05-25 16:12:45] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -e NGINX_PORT=80 -e API_URL=http://localhost:9080 ewp-edge:latest
[2025-05-25 16:12:46] [INFO] Container start output: 3cce5722902949b7769245a184654e45e40be5eca96008b0f1f9a48a78fac971
[2025-05-25 16:12:49] [INFO] ewp-edge container started successfully
[2025-05-25 16:12:49] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 16:12:49] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 16:12:49] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 16:12:49] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 16:12:49] [INFO] Found 30 files in source directory
[2025-05-25 16:12:49] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
PS>TerminatingError(Copy-Item): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'."
[2025-05-25 16:12:49] [ERROR] Error copying files: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'.
[2025-05-25 16:12:49] [INFO] Setting up startup shortcut...
[2025-05-25 16:12:49] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 16:12:49] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 16:12:49] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 16:12:49] [INFO] Monitor executable found, creating shortcut
[2025-05-25 16:12:49] [INFO] Startup shortcut created successfully
[2025-05-25 16:12:49] [INFO] Found existing Eco Edge Monitor processes, stopping them
[2025-05-25 16:12:49] [INFO] Process with ID 33296 stopped
[2025-05-25 16:12:49] [INFO] Stopped existing Eco Edge Monitor instances.
[2025-05-25 16:12:50] [INFO] Starting Eco Edge Monitor application
[2025-05-25 16:12:50] [INFO] Started Eco Edge Monitor with process ID: 3276
[2025-05-25 16:12:50] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 16:12:52] [INFO] Verified Eco Edge Monitor is running with process ID: 3276
[2025-05-25 16:12:52] [INFO] Eco Edge Agent installation complete.
Transcript stopped, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161228.log
Installation log saved to: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161228.log
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
]633;D]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>cd 'c:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package'
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\scripts\install.ps1 -ConfigFilePath "C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json"
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161448.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161448.log
[2025-05-25 16:14:48] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 16:14:48] [INFO] Checking for Podman installation...
Podman is already installed.
Using provided config file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json
[2025-05-25 16:14:48] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 16:14:48] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 107691520 bytes)
[2025-05-25 16:14:48] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 16:14:50] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 16:14:50] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52726784 bytes)
[2025-05-25 16:14:50] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 16:14:52] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
[2025-05-25 16:14:52] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 16:14:52] [INFO] Adding environment variable: REST_PORT=9080
[2025-05-25 16:14:52] [INFO] Adding environment variable: REST_HOST=0.0.0.0
[2025-05-25 16:14:52] [INFO] Adding environment variable: CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 16:14:52] [INFO] Adding environment variable: CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem
[2025-05-25 16:14:52] [INFO] Adding environment variable: CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem
[2025-05-25 16:14:52] [INFO] Adding environment variable: CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem
[2025-05-25 16:14:52] [INFO] Adding environment variable: CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt
[2025-05-25 16:14:52] [INFO] Adding environment variable: CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com
[2025-05-25 16:14:52] [INFO] Adding environment variable: DB_PATH=/app/db
[2025-05-25 16:14:52] [INFO] Adding environment variable: AUTH_DB_PATH=/app/db/auth/auth.db
[2025-05-25 16:14:52] [INFO] Adding environment variable: AUTH_CONFIG_DB_PATH=/app/db/auth/config.db
[2025-05-25 16:14:52] [INFO] Adding environment variable: SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db
[2025-05-25 16:14:52] [INFO] Adding environment variable: CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db
[2025-05-25 16:14:52] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 16:14:52] [INFO] Adding environment variable: API_URL=http://locolhost:9080
[2025-05-25 16:14:52] [INFO] Adding environment variable: NGINX_PORT=80
[2025-05-25 16:14:52] [INFO] Using volume mappings from config file
[2025-05-25 16:14:52] [INFO] Processing volume mappings from hashtable format
[2025-05-25 16:14:52] [INFO] Adding volume mapping: C:\data1\backend\logs -> /app/logs
[2025-05-25 16:14:52] [INFO] Adding volume mapping: C:\data1\backend\certs -> /app/certs
[2025-05-25 16:14:52] [INFO] Adding volume mapping: C:\data1\backend\db -> /app/db
[2025-05-25 16:14:52] [INFO] Checking if eco-edge-network exists...
[2025-05-25 16:14:52] [INFO] eco-edge-network already exists
[2025-05-25 16:14:52] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER b6856a0b93cb  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 16:14:52] [INFO] Checking for existing containers...
[2025-05-25 16:14:53] [INFO] Found existing container: octopus-service
[2025-05-25 16:14:53] [INFO] Stopping container: octopus-service
[2025-05-25 16:15:01] [INFO] Stop container output: octopus-service
[2025-05-25 16:15:01] [INFO] Removing container: octopus-service
[2025-05-25 16:15:02] [INFO] Remove container output: octopus-service
[2025-05-25 16:15:02] [INFO] Found existing container: ewp-edge
[2025-05-25 16:15:02] [INFO] Stopping container: ewp-edge
[2025-05-25 16:15:04] [INFO] Stop container output: ewp-edge
[2025-05-25 16:15:04] [INFO] Removing container: ewp-edge
[2025-05-25 16:15:04] [INFO] Remove container output: ewp-edge
[2025-05-25 16:15:04] [INFO] Running octopus-service container...
[2025-05-25 16:15:04] [INFO] Added backend environment arguments: -e REST_HOST=0.0.0.0 -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e DB_PATH=/app/db -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_PORT=9080 -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db
[2025-05-25 16:15:04] [INFO] Added volume arguments: -v C:\data1\backend\db:/app/db -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\certs:/app/certs
[2025-05-25 16:15:04] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -e REST_HOST=0.0.0.0 -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e DB_PATH=/app/db -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_PORT=9080 -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db -v C:\data1\backend\db:/app/db -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\certs:/app/certs --user 1000:1000 octopus-service:latest
[2025-05-25 16:15:05] [INFO] Container start output: Error: statfs /mnt/c/data1/backend/db: no such file or directory
[2025-05-25 16:15:07] [ERROR] octopus-service container failed to start or stopped immediately
[2025-05-25 16:15:07] [ERROR] Container logs:\nError: no container with name or ID "octopus-service" found: no such container
[2025-05-25 16:15:07] [INFO] Running ewp-edge container...
[2025-05-25 16:15:07] [INFO] Added frontend environment arguments: -e API_URL=http://locolhost:9080 -e NGINX_PORT=80
[2025-05-25 16:15:07] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -e API_URL=http://locolhost:9080 -e NGINX_PORT=80 ewp-edge:latest
[2025-05-25 16:15:09] [INFO] Container start output: 74400b78002646b9c3a8f19b536dbcb93f8305b2c70d8e5e7c16816b884cc5b5
[2025-05-25 16:15:12] [INFO] ewp-edge container started successfully
[2025-05-25 16:15:12] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 16:15:12] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 16:15:12] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 16:15:12] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 16:15:12] [INFO] Found 30 files in source directory
[2025-05-25 16:15:12] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
PS>TerminatingError(Copy-Item): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'."
[2025-05-25 16:15:12] [ERROR] Error copying files: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'.
[2025-05-25 16:15:12] [INFO] Setting up startup shortcut...
[2025-05-25 16:15:12] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 16:15:12] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 16:15:12] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 16:15:12] [INFO] Monitor executable found, creating shortcut
[2025-05-25 16:15:12] [INFO] Startup shortcut created successfully
[2025-05-25 16:15:12] [INFO] Found existing Eco Edge Monitor processes, stopping them
[2025-05-25 16:15:12] [INFO] Process with ID 3276 stopped
[2025-05-25 16:15:12] [INFO] Stopped existing Eco Edge Monitor instances.
[2025-05-25 16:15:13] [INFO] Starting Eco Edge Monitor application
[2025-05-25 16:15:13] [INFO] Started Eco Edge Monitor with process ID: 39220
[2025-05-25 16:15:13] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 16:15:15] [INFO] Verified Eco Edge Monitor is running with process ID: 39220
[2025-05-25 16:15:15] [INFO] Eco Edge Agent installation complete.
Transcript stopped, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161448.log
Installation log saved to: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161448.log
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\scripts\install.ps1 -ConfigFilePath "C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json"
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161940.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161940.log
[2025-05-25 16:19:40] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 16:19:40] [INFO] Checking for Podman installation...
Podman is already installed.
Using provided config file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json
[2025-05-25 16:19:40] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 16:19:40] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 107691520 bytes)
[2025-05-25 16:19:40] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 16:19:42] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 16:19:42] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52726784 bytes)
[2025-05-25 16:19:42] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 16:19:43] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
[2025-05-25 16:19:43] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 16:19:43] [INFO] Adding environment variable: REST_PORT=9080
[2025-05-25 16:19:43] [INFO] Adding environment variable: REST_HOST=0.0.0.0
[2025-05-25 16:19:43] [INFO] Adding environment variable: CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 16:19:43] [INFO] Adding environment variable: CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem
[2025-05-25 16:19:43] [INFO] Adding environment variable: CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem
[2025-05-25 16:19:43] [INFO] Adding environment variable: CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem
[2025-05-25 16:19:43] [INFO] Adding environment variable: CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt
[2025-05-25 16:19:43] [INFO] Adding environment variable: CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com
[2025-05-25 16:19:43] [INFO] Adding environment variable: DB_PATH=/app/db
[2025-05-25 16:19:43] [INFO] Adding environment variable: AUTH_DB_PATH=/app/db/auth/auth.db
[2025-05-25 16:19:43] [INFO] Adding environment variable: AUTH_CONFIG_DB_PATH=/app/db/auth/config.db
[2025-05-25 16:19:43] [INFO] Adding environment variable: SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db
[2025-05-25 16:19:43] [INFO] Adding environment variable: CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db
[2025-05-25 16:19:43] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 16:19:43] [INFO] Adding environment variable: API_URL=http://locolhost:9080
[2025-05-25 16:19:43] [INFO] Adding environment variable: NGINX_PORT=80
[2025-05-25 16:19:43] [INFO] Using volume mappings from config file
[2025-05-25 16:19:43] [INFO] Processing volume mappings from hashtable format
[2025-05-25 16:19:43] [INFO] Adding volume mapping: C:\data1\backend\logs -> /app/logs
[2025-05-25 16:19:43] [INFO] Adding volume mapping: C:\data1\backend\certs -> /app/certs
[2025-05-25 16:19:43] [INFO] Adding volume mapping: C:\data1\backend\db -> /app/db
[2025-05-25 16:19:43] [INFO] Checking and creating host directories for volume mappings...
[2025-05-25 16:19:43] [INFO] Creating directory: C:\data1\backend\db
[2025-05-25 16:19:43] [INFO] Creating directory: C:\data1\backend\logs
[2025-05-25 16:19:43] [INFO] Creating directory: C:\data1\backend\certs
[2025-05-25 16:19:43] [INFO] Checking if eco-edge-network exists...
[2025-05-25 16:19:43] [INFO] eco-edge-network already exists
[2025-05-25 16:19:44] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER b6856a0b93cb  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 16:19:44] [INFO] Checking for existing containers...
[2025-05-25 16:19:44] [INFO] Container octopus-service does not exist, no need to remove
[2025-05-25 16:19:44] [INFO] Found existing container: ewp-edge
[2025-05-25 16:19:45] [INFO] Stopping container: ewp-edge
[2025-05-25 16:19:46] [INFO] Stop container output: ewp-edge
[2025-05-25 16:19:46] [INFO] Removing container: ewp-edge
[2025-05-25 16:19:47] [INFO] Remove container output: ewp-edge
[2025-05-25 16:19:47] [INFO] Running octopus-service container...
[2025-05-25 16:19:47] [INFO] Added backend environment arguments: -e REST_HOST=0.0.0.0 -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e DB_PATH=/app/db -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_PORT=9080 -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db
[2025-05-25 16:19:47] [INFO] Added volume arguments: -v C:\data1\backend\db:/app/db -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\certs:/app/certs
[2025-05-25 16:19:47] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -e REST_HOST=0.0.0.0 -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e DB_PATH=/app/db -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_PORT=9080 -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db -v C:\data1\backend\db:/app/db -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\certs:/app/certs --user 1000:1000 octopus-service:latest
[2025-05-25 16:19:49] [INFO] Container start output: 4edb18e64e830264ca8d9ab47e2240e41d502e5416aa1f0b0e0c53517bd99911
[2025-05-25 16:19:51] [INFO] octopus-service container started successfully
[2025-05-25 16:19:51] [INFO] Running ewp-edge container...
[2025-05-25 16:19:51] [INFO] Added frontend environment arguments: -e API_URL=http://locolhost:9080 -e NGINX_PORT=80
[2025-05-25 16:19:51] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -e API_URL=http://locolhost:9080 -e NGINX_PORT=80 ewp-edge:latest
[2025-05-25 16:19:53] [INFO] Container start output: 98c9f45d120a46e339579b418fec8b4c3cfb0dd087b839177f56c25150e9abf7
[2025-05-25 16:19:56] [INFO] ewp-edge container started successfully
[2025-05-25 16:19:56] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 16:19:56] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 16:19:56] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 16:19:56] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 16:19:56] [INFO] Found 30 files in source directory
[2025-05-25 16:19:56] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
PS>TerminatingError(Copy-Item): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'."
[2025-05-25 16:19:56] [ERROR] Error copying files: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'.
[2025-05-25 16:19:56] [INFO] Setting up startup shortcut...
[2025-05-25 16:19:56] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 16:19:56] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 16:19:56] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 16:19:56] [INFO] Monitor executable found, creating shortcut
[2025-05-25 16:19:56] [INFO] Startup shortcut created successfully
[2025-05-25 16:19:56] [INFO] Found existing Eco Edge Monitor processes, stopping them
[2025-05-25 16:19:56] [INFO] Process with ID 39220 stopped
[2025-05-25 16:19:56] [INFO] Stopped existing Eco Edge Monitor instances.
[2025-05-25 16:19:57] [INFO] Starting Eco Edge Monitor application
[2025-05-25 16:19:57] [INFO] Started Eco Edge Monitor with process ID: 33236
[2025-05-25 16:19:57] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 16:19:59] [INFO] Verified Eco Edge Monitor is running with process ID: 33236
[2025-05-25 16:19:59] [INFO] Eco Edge Agent installation complete.
Transcript stopped, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161940.log
Installation log saved to: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_161940.log
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\scripts\install.ps1 -ConfigFilePath "C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json"
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_162204.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_162204.log
[2025-05-25 16:22:04] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 16:22:04] [INFO] Checking for Podman installation...
Podman is already installed.
Using provided config file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json
[2025-05-25 16:22:04] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 16:22:04] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 107691520 bytes)
[2025-05-25 16:22:04] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 16:22:06] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 16:22:06] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52726784 bytes)
[2025-05-25 16:22:06] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 16:22:07] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
[2025-05-25 16:22:07] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 16:22:07] [INFO] Adding environment variable: REST_PORT=9080
[2025-05-25 16:22:07] [INFO] Adding environment variable: REST_HOST=0.0.0.0
[2025-05-25 16:22:07] [INFO] Adding environment variable: CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 16:22:07] [INFO] Adding environment variable: CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem
[2025-05-25 16:22:07] [INFO] Adding environment variable: CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem
[2025-05-25 16:22:07] [INFO] Adding environment variable: CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem
[2025-05-25 16:22:07] [INFO] Adding environment variable: CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt
[2025-05-25 16:22:07] [INFO] Adding environment variable: CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com
[2025-05-25 16:22:07] [INFO] Adding environment variable: DB_PATH=/app/db
[2025-05-25 16:22:07] [INFO] Adding environment variable: AUTH_DB_PATH=/app/db/auth/auth.db
[2025-05-25 16:22:07] [INFO] Adding environment variable: AUTH_CONFIG_DB_PATH=/app/db/auth/config.db
[2025-05-25 16:22:07] [INFO] Adding environment variable: SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db
[2025-05-25 16:22:07] [INFO] Adding environment variable: CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db
[2025-05-25 16:22:07] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 16:22:07] [INFO] Adding environment variable: API_URL=http://locolhost:9080
[2025-05-25 16:22:07] [INFO] Adding environment variable: NGINX_PORT=80
[2025-05-25 16:22:07] [INFO] Using volume mappings from config file
[2025-05-25 16:22:07] [INFO] Processing volume mappings from hashtable format
[2025-05-25 16:22:07] [INFO] Adding volume mapping: C:\data1\backend\logs -> /app/logs
[2025-05-25 16:22:07] [INFO] Adding volume mapping: C:\data1\backend\certs -> /app/certs
[2025-05-25 16:22:07] [INFO] Adding volume mapping: C:\data1\backend\db -> /app/db
[2025-05-25 16:22:07] [INFO] Checking and creating host directories for volume mappings...
[2025-05-25 16:22:07] [INFO] Directory already exists: C:\data1\backend\db
[2025-05-25 16:22:07] [INFO] Directory already exists: C:\data1\backend\logs
[2025-05-25 16:22:07] [INFO] Directory already exists: C:\data1\backend\certs
[2025-05-25 16:22:07] [INFO] Checking if eco-edge-network exists...
[2025-05-25 16:22:08] [INFO] eco-edge-network already exists
[2025-05-25 16:22:08] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER b6856a0b93cb  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 16:22:08] [INFO] Checking for existing containers...
[2025-05-25 16:22:08] [INFO] Found existing container: octopus-service
[2025-05-25 16:22:09] [INFO] Stopping container: octopus-service
[2025-05-25 16:22:12] [INFO] Stop container output: octopus-service
[2025-05-25 16:22:12] [INFO] Removing container: octopus-service
[2025-05-25 16:22:12] [INFO] Remove container output: octopus-service
[2025-05-25 16:22:13] [INFO] Found existing container: ewp-edge
[2025-05-25 16:22:13] [INFO] Stopping container: ewp-edge
[2025-05-25 16:22:15] [INFO] Stop container output: ewp-edge
[2025-05-25 16:22:15] [INFO] Removing container: ewp-edge
[2025-05-25 16:22:15] [INFO] Remove container output: ewp-edge
[2025-05-25 16:22:15] [INFO] Running octopus-service container...
[2025-05-25 16:22:15] [INFO] Added backend environment arguments: -e REST_HOST=0.0.0.0 -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e DB_PATH=/app/db -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_PORT=9080 -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db
[2025-05-25 16:22:15] [INFO] Added volume arguments: -v C:\data1\backend\db:/app/db -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\certs:/app/certs
[2025-05-25 16:22:15] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -e REST_HOST=0.0.0.0 -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e DB_PATH=/app/db -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_PORT=9080 -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db -v C:\data1\backend\db:/app/db -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\certs:/app/certs --user 1000:1000 octopus-service:latest
[2025-05-25 16:22:17] [INFO] Container start output: aee46699b856131a10ca1c40bbe4efc79f40887de4c9046e03364a7ff26134d0
[2025-05-25 16:22:20] [INFO] octopus-service container started successfully
[2025-05-25 16:22:20] [INFO] Running ewp-edge container...
[2025-05-25 16:22:20] [INFO] Added frontend environment arguments: -e API_URL=http://locolhost:9080 -e NGINX_PORT=80
[2025-05-25 16:22:20] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -e API_URL=http://locolhost:9080 -e NGINX_PORT=80 ewp-edge:latest
[2025-05-25 16:22:22] [INFO] Container start output: fab263bc0854fdaf4a2aa86488f28314b85b09a04e8040cb7105fd62b64918ab
[2025-05-25 16:22:24] [INFO] ewp-edge container started successfully
[2025-05-25 16:22:24] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 16:22:24] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 16:22:24] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 16:22:24] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 16:22:24] [INFO] Found 30 files in source directory
[2025-05-25 16:22:24] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
PS>TerminatingError(Copy-Item): "The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'."
[2025-05-25 16:22:24] [ERROR] Error copying files: The requested operation cannot be performed on a file with a user-mapped section open. : 'C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\data\icudtl.dat'.
[2025-05-25 16:22:24] [INFO] Setting up startup shortcut...
[2025-05-25 16:22:24] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 16:22:24] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 16:22:24] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 16:22:24] [INFO] Monitor executable found, creating shortcut
[2025-05-25 16:22:24] [INFO] Startup shortcut created successfully
[2025-05-25 16:22:24] [INFO] Found existing Eco Edge Monitor processes, stopping them
[2025-05-25 16:22:24] [INFO] Stopping process with ID 33236
True
[2025-05-25 16:22:24] [INFO] Process with ID 33236 stopped
[2025-05-25 16:22:24] [INFO] Stopped existing Eco Edge Monitor instances.
[2025-05-25 16:22:24] [INFO] Waiting for resources to be released...
[2025-05-25 16:22:27] [INFO] Checking for locked files in C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 16:22:27] [INFO] No locked files detected
[2025-05-25 16:22:27] [INFO] Starting Eco Edge Monitor application
[2025-05-25 16:22:27] [INFO] Started Eco Edge Monitor with process ID: 39764
[2025-05-25 16:22:27] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 16:22:29] [INFO] Verified Eco Edge Monitor is running with process ID: 39764
[2025-05-25 16:22:29] [INFO] Eco Edge Agent installation complete.
Transcript stopped, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_162204.log
Installation log saved to: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_162204.log
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\scripts\install.ps1 -ConfigFilePath "C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json"
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_162523.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_162523.log
[2025-05-25 16:25:23] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 16:25:23] [INFO] Checking for Podman installation...
Podman is already installed.
Using provided config file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json
[2025-05-25 16:25:23] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 16:25:23] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 107691520 bytes)
[2025-05-25 16:25:23] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 16:25:25] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 16:25:25] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52726784 bytes)
[2025-05-25 16:25:25] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 16:25:26] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
[2025-05-25 16:25:26] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 16:25:26] [INFO] Adding environment variable: REST_PORT=9080
[2025-05-25 16:25:26] [INFO] Adding environment variable: REST_HOST=0.0.0.0
[2025-05-25 16:25:26] [INFO] Adding environment variable: CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 16:25:26] [INFO] Adding environment variable: CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem
[2025-05-25 16:25:26] [INFO] Adding environment variable: CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem
[2025-05-25 16:25:26] [INFO] Adding environment variable: CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem
[2025-05-25 16:25:26] [INFO] Adding environment variable: CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt
[2025-05-25 16:25:26] [INFO] Adding environment variable: CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com
[2025-05-25 16:25:26] [INFO] Adding environment variable: DB_PATH=/app/db
[2025-05-25 16:25:26] [INFO] Adding environment variable: AUTH_DB_PATH=/app/db/auth/auth.db
[2025-05-25 16:25:26] [INFO] Adding environment variable: AUTH_CONFIG_DB_PATH=/app/db/auth/config.db
[2025-05-25 16:25:26] [INFO] Adding environment variable: SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db
[2025-05-25 16:25:26] [INFO] Adding environment variable: CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db
[2025-05-25 16:25:26] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 16:25:26] [INFO] Adding environment variable: API_URL=http://locolhost:9080
[2025-05-25 16:25:26] [INFO] Adding environment variable: NGINX_PORT=80
[2025-05-25 16:25:26] [INFO] Using volume mappings from config file
[2025-05-25 16:25:26] [INFO] Processing volume mappings from hashtable format
[2025-05-25 16:25:26] [INFO] Adding volume mapping: C:\data1\backend\logs -> /app/logs
[2025-05-25 16:25:26] [INFO] Adding volume mapping: C:\data1\backend\certs -> /app/certs
[2025-05-25 16:25:26] [INFO] Adding volume mapping: C:\data1\backend\db -> /app/db
[2025-05-25 16:25:26] [INFO] Checking and creating host directories for volume mappings...
[2025-05-25 16:25:26] [INFO] Directory already exists: C:\data1\backend\db
[2025-05-25 16:25:26] [INFO] Directory already exists: C:\data1\backend\logs
[2025-05-25 16:25:26] [INFO] Directory already exists: C:\data1\backend\certs
[2025-05-25 16:25:26] [INFO] Checking if eco-edge-network exists...
[2025-05-25 16:25:27] [INFO] eco-edge-network already exists
[2025-05-25 16:25:27] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER b6856a0b93cb  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 16:25:27] [INFO] Checking for existing containers...
[2025-05-25 16:25:27] [INFO] Found existing container: octopus-service
[2025-05-25 16:25:28] [INFO] Stopping container: octopus-service
[2025-05-25 16:25:36] [INFO] Stop container output: octopus-service
[2025-05-25 16:25:36] [INFO] Removing container: octopus-service
[2025-05-25 16:25:37] [INFO] Remove container output: octopus-service
[2025-05-25 16:25:37] [INFO] Found existing container: ewp-edge
[2025-05-25 16:25:37] [INFO] Stopping container: ewp-edge
[2025-05-25 16:25:39] [INFO] Stop container output: ewp-edge
[2025-05-25 16:25:39] [INFO] Removing container: ewp-edge
[2025-05-25 16:25:39] [INFO] Remove container output: ewp-edge
[2025-05-25 16:25:39] [INFO] Running octopus-service container...
[2025-05-25 16:25:39] [INFO] Added backend environment arguments: -e REST_HOST=0.0.0.0 -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e DB_PATH=/app/db -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_PORT=9080 -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db
[2025-05-25 16:25:39] [INFO] Added volume arguments: -v C:\data1\backend\db:/app/db -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\certs:/app/certs
[2025-05-25 16:25:39] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -e REST_HOST=0.0.0.0 -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e DB_PATH=/app/db -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e REST_PORT=9080 -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db -v C:\data1\backend\db:/app/db -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\certs:/app/certs --user 1000:1000 octopus-service:latest
[2025-05-25 16:25:41] [INFO] Container start output: 7d68dc867e3596cce8d3498995d9b918695a84efda4d9f72bcdd96e472a9771d
[2025-05-25 16:25:44] [INFO] octopus-service container started successfully
[2025-05-25 16:25:44] [INFO] Running ewp-edge container...
[2025-05-25 16:25:44] [INFO] Added frontend environment arguments: -e API_URL=http://locolhost:9080 -e NGINX_PORT=80
[2025-05-25 16:25:44] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -e API_URL=http://locolhost:9080 -e NGINX_PORT=80 ewp-edge:latest
[2025-05-25 16:25:46] [INFO] Container start output: 6b85e32cd437ae9a6121919d7d1af2b98597125c4bd8d2c26049e53f9ba59abb
[2025-05-25 16:25:48] [INFO] ewp-edge container started successfully
[2025-05-25 16:25:48] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 16:25:48] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 16:25:48] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 16:25:48] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 16:25:48] [INFO] Found 30 files in source directory
[2025-05-25 16:25:48] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 16:25:48] [INFO] Checking for locked files in source directory...
[2025-05-25 16:25:48] [INFO] Using robocopy for more reliable file copying
[2025-05-25 16:25:48] [INFO] Files copied successfully
[2025-05-25 16:25:48] [INFO] Setting up startup shortcut...
[2025-05-25 16:25:48] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 16:25:48] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 16:25:48] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 16:25:48] [INFO] Monitor executable found, creating shortcut
[2025-05-25 16:25:48] [INFO] Startup shortcut created successfully
[2025-05-25 16:25:48] [INFO] Found existing Eco Edge Monitor processes, stopping them
[2025-05-25 16:25:48] [INFO] Stopping process with ID 39764
True
[2025-05-25 16:25:48] [INFO] Process with ID 39764 stopped
[2025-05-25 16:25:48] [INFO] Stopped existing Eco Edge Monitor instances.
[2025-05-25 16:25:48] [INFO] Waiting for resources to be released...
[2025-05-25 16:25:51] [INFO] Checking for locked files in C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 16:25:51] [INFO] No locked files detected
[2025-05-25 16:25:51] [INFO] Starting Eco Edge Monitor application
[2025-05-25 16:25:51] [INFO] Started Eco Edge Monitor with process ID: 13612
[2025-05-25 16:25:51] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 16:25:53] [INFO] Verified Eco Edge Monitor is running with process ID: 13612
[2025-05-25 16:25:53] [INFO] Eco Edge Agent installation complete.
Transcript stopped, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_162523.log
Installation log saved to: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_162523.log
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\build.ps1
Using WiX Toolset from: C:\Program Files (x86)\WiX Toolset v3.11\bin
Using output directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output
Found custom action DLL at: CustomAction\x64\FileBrowse.dll
Compiling WiX source files...
Creating a basic MSI file for demonstration purposes...
WiX Toolset found at: C:\Program Files (x86)\WiX Toolset v3.11\bin
Generating WiX fragment for flutter_assets directory...

Successfully generated WiX fragment for flutter_assets directory
Successfully fixed paths in FlutterAssets.wxs

Successfully compiled flutter_assets fragment
Running candle.exe...

Running light.exe...
Removing existing MSI file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output\ESXPEdgeAgent.msi
Successfully created C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output\ESXPEdgeAgent.msi
Build completed successfully!
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\build.ps1
Using WiX Toolset from: C:\Program Files (x86)\WiX Toolset v3.11\bin
Using output directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output
Found custom action DLL at: CustomAction\x64\FileBrowse.dll
Compiling WiX source files...
Creating a basic MSI file for demonstration purposes...
WiX Toolset found at: C:\Program Files (x86)\WiX Toolset v3.11\bin
Generating WiX fragment for flutter_assets directory...

Successfully generated WiX fragment for flutter_assets directory
Successfully fixed paths in FlutterAssets.wxs

Successfully compiled flutter_assets fragment
Running candle.exe...

Running light.exe...
Removing existing MSI file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output\ESXPEdgeAgent.msi
Successfully created C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output\ESXPEdgeAgent.msi
Build completed successfully!
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\build.ps1
Using WiX Toolset from: C:\Program Files (x86)\WiX Toolset v3.11\bin
Using output directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output
Found custom action DLL at: CustomAction\x64\FileBrowse.dll
Compiling WiX source files...
Creating a basic MSI file for demonstration purposes...
WiX Toolset found at: C:\Program Files (x86)\WiX Toolset v3.11\bin
Generating WiX fragment for flutter_assets directory...

Successfully generated WiX fragment for flutter_assets directory
Successfully fixed paths in FlutterAssets.wxs

Successfully compiled flutter_assets fragment
Running candle.exe...

Running light.exe...
Removing existing MSI file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output\ESXPEdgeAgent.msi
Successfully created C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output\ESXPEdgeAgent.msi
Build completed successfully!
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>cd 'c:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package'
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>.\build.ps1
Using WiX Toolset from: C:\Program Files (x86)\WiX Toolset v3.11\bin
Using output directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output
Found custom action DLL at: CustomAction\x64\FileBrowse.dll
Compiling WiX source files...
Creating a basic MSI file for demonstration purposes...
WiX Toolset found at: C:\Program Files (x86)\WiX Toolset v3.11\bin
Generating WiX fragment for flutter_assets directory...

Successfully generated WiX fragment for flutter_assets directory
Successfully fixed paths in FlutterAssets.wxs

Successfully compiled flutter_assets fragment
Running candle.exe...

Running light.exe...
Removing existing MSI file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output\ESXPEdgeAgent.msi
Successfully created C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\output\ESXPEdgeAgent.msi
Build completed successfully!
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>git push
git: The term 'git' is not recognized as a name of a cmdlet, function, script file, or executable program.
Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
git: The term 'git' is not recognized as a name of a cmdlet, function, script file, or executable program.
Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
]633;D;1]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>c ..
c: The term 'c' is not recognized as a name of a cmdlet, function, script file, or executable program.
Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
c: The term 'c' is not recognized as a name of a cmdlet, function, script file, or executable program.
Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
]633;D;1]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>cd ..
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agentPS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent> ]633;B
PS>git push
git: The term 'git' is not recognized as a name of a cmdlet, function, script file, or executable program.
Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
git: The term 'git' is not recognized as a name of a cmdlet, function, script file, or executable program.
Check the spelling of the name, or if a path was included, verify that the path is correct and try again.
]633;D;1]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agentPS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent> ]633;B
