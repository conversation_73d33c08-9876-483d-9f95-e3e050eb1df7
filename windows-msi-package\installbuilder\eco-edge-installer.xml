<?xml version="1.0" encoding="UTF-8"?>
<project>
    <shortName>ecoedgeagent</shortName>
    <fullName>Eco Edge Agent</fullName>
    <version>1.1.2</version>
    <vendor>Eco Edge Team</vendor>
    <windowsExecutableVersionInfo>
        <productVersion>1.1.2</productVersion>
        <fileVersion>1.1.2</fileVersion>
        <companyName>Eco Edge Team</companyName>
        <fileDescription>Eco Edge Agent</fileDescription>
        <legalCopyright>© 2025 Eco Edge Team</legalCopyright>
        <productName>Eco Edge Agent</productName>
    </windowsExecutableVersionInfo>
    <customLanguageFileList>
        <language>
            <code>en</code>
            <encoding>iso8859-1</encoding>
            <file>en.xml</file>
        </language>
    </customLanguageFileList>
    <componentList>
        <!-- Main Program Files -->
        <component>
            <name>program_files</name>
            <description>Program Files</description>
            <canBeEdited>0</canBeEdited>
            <selected>1</selected>
            <show>1</show>
            <folderList>
                <folder>
                    <description>Program Files</description>
                    <destination>${installdir}</destination>
                    <name>programfiles</name>
                    <platforms>windows</platforms>
                    <distributionFileList>
                        <distributionFile>
                            <origin>scripts/install.ps1</origin>
                            <allowWildcards>1</allowWildcards>
                        </distributionFile>
                        <!-- Add other required files here -->
                    </distributionFileList>
                    <shortcutList>
                        <shortcut>
                            <comment>Uninstall</comment>
                            <exec>${installdir}/${uninstallerName}</exec>
                            <icon></icon>
                            <name>Uninstall ${product_fullname}</name>
                            <path>${installdir}</path>
                            <platforms>windows</platforms>
                            <runAsAdmin>0</runAsAdmin>
                            <runInTerminal>0</runInTerminal>
                            <windowsExec>${installdir}/${uninstallerName}.exe</windowsExec>
                            <windowsExecArgs></windowsExecArgs>
                            <windowsIcon></windowsIcon>
                            <windowsPath>${installdir}</windowsPath>
                        </shortcut>
                    </shortcutList>
                </folder>
            </folderList>
            <startMenuShortcutList>
                <startMenuShortcut>
                    <comment>Uninstall ${product_fullname}</comment>
                    <name>Uninstall ${product_fullname}</name>
                    <runAsAdmin>0</runAsAdmin>
                    <runInTerminal>0</runInTerminal>
                    <windowsExec>${installdir}/${uninstallerName}.exe</windowsExec>
                    <windowsExecArgs></windowsExecArgs>
                    <windowsIcon></windowsIcon>
                    <windowsPath>${installdir}</windowsPath>
                </startMenuShortcut>
            </startMenuShortcutList>
        </component>
        
        <!-- Configuration File Selection Component -->
        <component>
            <name>config_file</name>
            <description>Configuration</description>
            <canBeEdited>1</canBeEdited>
            <selected>1</selected>
            <show>1</show>
            <parameterList>
                <fileParameter>
                    <name>config_file_path</name>
                    <title>Configuration File</title>
                    <description>Optional: Select a configuration file for Eco Edge Agent</description>
                    <explanation>If no file is selected, default values will be used.</explanation>
                    <value></value>
                    <default></default>
                    <allowEmptyValue>1</allowEmptyValue>
                    <mustExist>0</mustExist>
                    <width>40</width>
                </fileParameter>
            </parameterList>
        </component>
        
        <!-- Auto-start Configuration -->
        <component>
            <name>auto_start</name>
            <description>Auto-start Settings</description>
            <canBeEdited>1</canBeEdited>
            <selected>1</selected>
            <show>1</show>
            <parameterList>
                <booleanParameter>
                    <name>auto_start_enabled</name>
                    <title>Start containers automatically</title>
                    <description>Start Eco Edge containers automatically on system boot</description>
                    <explanation>This will configure the containers to start automatically when your system boots.</explanation>
                    <value>1</value>
                    <default>1</default>
                </booleanParameter>
                <booleanParameter>
                    <name>recovery_enabled</name>
                    <title>Enable container auto-recovery</title>
                    <description>Automatically restart containers if they exit unexpectedly</description>
                    <explanation>This will enable the container auto-recovery mechanism to restart containers if they exit.</explanation>
                    <value>1</value>
                    <default>1</default>
                </booleanParameter>
            </parameterList>
        </component>
    </componentList>
    
    <preBuildActionList>
        <!-- Add any pre-build actions if needed -->
    </preBuildActionList>
    
    <readyToInstallActionList>
        <!-- Actions before installation begins -->
        <showInfo>
            <text>Ready to install Eco Edge Agent.

Configuration File: ${config_file_path}
Auto-start Containers: ${auto_start_enabled}
Container Auto-recovery: ${recovery_enabled}

Click Install to proceed.</text>
        </showInfo>
    </readyToInstallActionList>
    
    <preInstallationActionList>
        <!-- Check Podman installation -->
        <actionGroup>
            <actionList>
                <showInfo>
                    <text>Podman installation was not detected. Eco Edge Agent requires Podman to run.

The installer will now attempt to install Podman.</text>
                </showInfo>
                <!-- Add Podman installation logic here -->
                <runProgram>
                    <program>powershell.exe</program>
                    <programArguments>-ExecutionPolicy Bypass -Command "& { Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1')) }"</programArguments>
                    <workingDirectory>${windows_folder_system}</workingDirectory>
                    <ruleList>
                        <programTest>
                            <name>choco</name>
                            <condition>not_exists</condition>
                        </programTest>
                    </ruleList>
                </runProgram>
                <runProgram>
                    <program>choco</program>
                    <programArguments>install podman -y</programArguments>
                    <workingDirectory>${windows_folder_system}</workingDirectory>
                </runProgram>
            </actionList>
            <ruleList>
                <fileTest>
                    <condition>not_exists</condition>
                    <path>C:\ProgramData\chocoportable\lib\podman-cli\tools\podman-5.4.0\usr\bin\podman.exe</path>
                    <ruleList>
                        <programTest>
                            <name>podman</name>
                            <condition>not_exists</condition>
                        </programTest>
                    </ruleList>
                </fileTest>
            </ruleList>
        </actionGroup>
    </preInstallationActionList>
    
    <postInstallationActionList>
        <!-- Save configuration settings -->
        <createFile>
            <path>${installdir}/config/settings.json</path>
            <text>{
  "auto_start": ${auto_start_enabled},
  "recovery_enabled": ${recovery_enabled}
}</text>
        </createFile>
        
        <!-- Run install.ps1 with the config file -->
        <actionGroup>
            <actionList>
                <!-- Run with config file -->
                <runProgram>
                    <program>powershell.exe</program>
                    <programArguments>-ExecutionPolicy Bypass -File "${installdir}\install.ps1" -ConfigFilePath "${config_file_path}"</programArguments>
                    <workingDirectory>${installdir}</workingDirectory>
                </runProgram>
            </actionList>
            <ruleList>
                <compareText>
                    <logic>does_not_equal</logic>
                    <text>${config_file_path}</text>
                    <value></value>
                </compareText>
            </ruleList>
        </actionGroup>
        
        <!-- Run install.ps1 without config file if none was selected -->
        <actionGroup>
            <actionList>
                <!-- Run without config file -->
                <runProgram>
                    <program>powershell.exe</program>
                    <programArguments>-ExecutionPolicy Bypass -File "${installdir}\install.ps1"</programArguments>
                    <workingDirectory>${installdir}</workingDirectory>
                </runProgram>
            </actionList>
            <ruleList>
                <compareText>
                    <logic>equals</logic>
                    <text>${config_file_path}</text>
                    <value></value>
                </compareText>
            </ruleList>
        </actionGroup>
        
        <!-- Configure auto-start if enabled -->
        <actionGroup>
            <actionList>
                <createScheduledTask>
                    <name>EcoEdgeAgentStartup</name>
                    <program>powershell.exe</program>
                    <arguments>-ExecutionPolicy Bypass -File "${installdir}\scripts\start-containers.ps1"</arguments>
                    <startOnBoot>1</startOnBoot>
                    <workingDirectory>${installdir}</workingDirectory>
                </createScheduledTask>
            </actionList>
            <ruleList>
                <isFalse>
                    <value>${auto_start_enabled}</value>
                    <negate>1</negate>
                </isFalse>
            </ruleList>
        </actionGroup>
        
        <!-- Show installation complete message -->
        <showInfo>
            <text>Eco Edge Agent has been successfully installed.

The application will manage the following Podman containers:
- eco-edge-service (Backend)
- eco-edge-interface (Frontend)

You can access the web interface by opening a browser and navigating to:
http://localhost:8080</text>
        </showInfo>
    </postInstallationActionList>
    
    <!-- Uninstallation actions -->
    <preUninstallationActionList>
        <showInfo>
            <text>Uninstalling Eco Edge Agent...

This will stop and remove the Eco Edge containers.</text>
        </showInfo>
        <runProgram>
            <program>powershell.exe</program>
            <programArguments>-ExecutionPolicy Bypass -File "${installdir}\scripts\cleanup.ps1"</programArguments>
            <workingDirectory>${installdir}</workingDirectory>
        </runProgram>
    </preUninstallationActionList>
    
    <finalPageActionList>
        <launchBrowser>
            <url>http://localhost:8080</url>
            <ruleList>
                <platformTest>
                    <type>windows</type>
                </platformTest>
            </ruleList>
        </launchBrowser>
    </finalPageActionList>
    
    <installationAbortedActionList>
        <showInfo>
            <text>Installation was aborted.</text>
        </showInfo>
    </installationAbortedActionList>
    
    <compressionAlgorithm>lzma</compressionAlgorithm>
    <enableRollback>1</enableRollback>
    <enableTimestamp>1</enableTimestamp>
    <outputDirectory>.</outputDirectory>
    <saveRelativePaths>1</saveRelativePaths>
    <vendor>Eco Edge Team</vendor>
    <windows64bitMode>1</windows64bitMode>
    <windowsResourceComments>Eco Edge Agent installation package</windowsResourceComments>
    <windowsResourceFileDescription>${product_fullname} Installer</windowsResourceFileDescription>
    <windowsResourceFileVersion>${product_version}</windowsResourceFileVersion>
    <windowsResourceLegalCopyright>© 2025 Eco Edge Team</windowsResourceLegalCopyright>
    <windowsResourceProductName>${product_fullname}</windowsResourceProductName>
    <windowsResourceProductVersion>${product_version}</windowsResourceProductVersion>
    <windowsUnattendedModeUI>minimal</windowsUnattendedModeUI>
</project>
