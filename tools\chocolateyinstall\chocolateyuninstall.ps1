$ErrorActionPreference = 'Continue'

try {
    Write-Host "Uninstalling Eco Edge application..."

    # Get installation info if available
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    $installDir = if ($isAdmin) { "$env:ProgramData\eco-edge" } else { "$env:LOCALAPPDATA\eco-edge" }
    $installInfoPath = Join-Path $installDir "install-info.json"

    if (Test-Path $installInfoPath) {
        try {
            $installInfo = Get-Content -Path $installInfoPath -Raw | ConvertFrom-Json
            Write-Host "Found installation info. Uninstalling from ports: Frontend=$($installInfo.FrontendPort), Backend=$($installInfo.BackendPort)"
        } catch {
            Write-Warning "Could not read installation info: $_"
        }
    }

    # Stop and remove application services
    podman rm -f octopus-service ewp-edge

    # Remove the network
    podman network rm eco-edge-network

    # Clean up application components
    podman rmi -f ewp-edge octopus-service

    # Remove desktop shortcut
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $shortcutFile = Join-Path $desktopPath "Eco Edge.url"
    if (Test-Path $shortcutFile) {
        Remove-Item $shortcutFile -Force
    }

    # Stop and remove system tray application

    # Find and kill the system tray process
    try {
        $processes = Get-Process -Name "eco_edge_monitor" -ErrorAction SilentlyContinue
        if ($processes) {
            $processes | ForEach-Object { $_.Kill() }
            Write-Host "Stopped Eco Edge Monitor system tray application."
        }
    } catch {
        Write-Warning "Could not stop Eco Edge Monitor process: $_"
    }

    # Remove startup shortcut
    $startupPath = [Environment]::GetFolderPath("Startup")
    $startupShortcutFile = Join-Path $startupPath "Eco Edge Monitor.lnk"
    if (Test-Path $startupShortcutFile) {
        Remove-Item $startupShortcutFile -Force
        Write-Host "Removed Eco Edge Monitor startup shortcut."
    }

    # Clean up installation directory
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    $installDir = if ($isAdmin) { "$env:ProgramData\eco-edge" } else { "$env:LOCALAPPDATA\eco-edge" }

    if (Test-Path $installDir) {
        Remove-Item -Path $installDir -Recurse -Force
    }

    Write-Host "Eco Edge has been successfully uninstalled."
}
catch {
    Write-Error "Uninstallation failed: $_"
    throw
}