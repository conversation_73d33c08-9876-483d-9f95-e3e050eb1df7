# Eco Edge System Tray Application Development

## Project Overview
We've developed a Flutter-based system tray application to monitor and manage the Eco Edge services. This application will be integrated with the existing Chocolatey package to provide users with an easy way to monitor and control the Eco Edge services.

## Application Features
1. **System Tray Integration**
   - Shows service status through icon colors (green for running, red for stopped, yellow for warnings)
   - Provides quick access to common actions through the tray menu

2. **Service Monitoring**
   - Displays real-time status of frontend and backend services
   - Shows resource usage (CPU, memory) for each service
   - Automatically refreshes status at configurable intervals

3. **Service Management**
   - Start, stop, and restart individual services
   - Start, stop, and restart all services at once
   - Open the web interface and API endpoints in the browser

4. **Settings Management**
   - Configure refresh interval
   - Set application to start minimized
   - Enable/disable auto-start on boot

## Project Structure
```
eco_edge_monitor/
├── assets/
│   ├── icons/                  # System tray and application icons
│   └── images/                 # Other application images
├── lib/
│   ├── models/
│   │   └── service_status.dart # Data models for service status
│   ├── providers/
│   │   └── eco_edge_provider.dart # State management
│   ├── screens/
│   │   └── home_screen.dart    # Main application screen
│   ├── services/
│   │   ├── podman_service.dart # Service for interacting with <PERSON><PERSON>
│   │   └── system_tray_manager.dart # System tray functionality
│   ├── widgets/
│   │   ├── service_card.dart   # UI component for displaying service info
│   │   └── settings_dialog.dart # Settings configuration dialog
│   └── main.dart              # Application entry point
└── build.ps1                  # Build script for Windows
```

## Integration with Chocolatey Package
The plan is to integrate this system tray application with the existing Chocolatey package by:

1. Building the Flutter application for Windows
2. Including the built application in the Chocolatey package
3. Installing the application during package installation
4. Creating desktop and startup shortcuts
5. Configuring the application to monitor the services installed by the package

## Next Steps
1. **Build the Flutter application** - Run the build script to create a Windows executable
2. **Update the Chocolatey installation script** - Modify the script to install the system tray application
3. **Test the integrated package** - Ensure the system tray application works correctly with the installed services
4. **Update documentation** - Add information about the system tray application to the package documentation

## Technical Details
- The application uses Podman commands to monitor and control the containers
- It communicates with the containers through a custom network setup
- The system tray functionality is implemented using the `system_tray` Flutter package
- State management is handled using the Provider pattern

This system tray application will significantly improve the user experience by providing an easy way to monitor and manage the Eco Edge services without having to use the command line or remember port numbers.
