**********************
PowerShell transcript start
Start time: 20250525191433
Username: APA\SESA808825
RunAs User: APA\SESA808825
Configuration Name: 
Machine: WIN01502063L (Microsoft Windows NT 10.0.22631.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -noexit -command try { . "c:\Users\<USER>\AppData\Local\Programs\Windsurf\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch {}
Process ID: 5540
PSVersion: 7.5.0
PSEdition: Core
GitCommitId: 7.5.0
OS: Microsoft Windows 10.0.22631
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_191433.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_191433.log
[2025-05-25 19:14:33] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 19:14:33] [INFO] Checking for Podman installation...
Podman is already installed.
Using provided config file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json
[2025-05-25 19:14:33] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 19:14:33] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 159188992 bytes)
[2025-05-25 19:14:33] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 19:14:43] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 19:14:43] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52736000 bytes)
[2025-05-25 19:14:43] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 19:14:45] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
Created data directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\data
[2025-05-25 19:14:45] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 19:14:45] [INFO] Adding environment variable: REST_PORT=9080
[2025-05-25 19:14:45] [INFO] Adding environment variable: REST_HOST=0.0.0.0
[2025-05-25 19:14:45] [INFO] Adding environment variable: CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 19:14:45] [INFO] Adding environment variable: CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem
[2025-05-25 19:14:45] [INFO] Adding environment variable: CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem
[2025-05-25 19:14:45] [INFO] Adding environment variable: CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem
[2025-05-25 19:14:45] [INFO] Adding environment variable: CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt
[2025-05-25 19:14:45] [INFO] Adding environment variable: CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com
[2025-05-25 19:14:45] [INFO] Adding environment variable: DB_PATH=/app/db
[2025-05-25 19:14:45] [INFO] Adding environment variable: AUTH_DB_PATH=/app/db/auth/auth.db
[2025-05-25 19:14:45] [INFO] Adding environment variable: AUTH_CONFIG_DB_PATH=/app/db/auth/config.db
[2025-05-25 19:14:45] [INFO] Adding environment variable: SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db
[2025-05-25 19:14:45] [INFO] Adding environment variable: CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db
[2025-05-25 19:14:45] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 19:14:45] [INFO] Adding environment variable: API_URL=http://locolhost:9080
[2025-05-25 19:14:45] [INFO] Adding environment variable: NGINX_PORT=80
[2025-05-25 19:14:45] [INFO] Using volume mappings from config file
[2025-05-25 19:14:45] [INFO] Processing volume mappings from hashtable format
[2025-05-25 19:14:45] [INFO] Adding volume mapping: C:\data1\backend\logs -> /app/logs
[2025-05-25 19:14:45] [INFO] Adding volume mapping: C:\data1\backend\certs -> /app/certs
[2025-05-25 19:14:45] [INFO] Adding volume mapping: C:\data1\backend\db -> /app/db
[2025-05-25 19:14:45] [INFO] Checking and creating host directories for volume mappings...
[2025-05-25 19:14:45] [INFO] Directory already exists: C:\data1\backend\logs
[2025-05-25 19:14:45] [INFO] Directory already exists: C:\data1\backend\db
[2025-05-25 19:14:45] [INFO] Directory already exists: C:\data1\backend\certs
[2025-05-25 19:14:45] [INFO] Checking if eco-edge-network exists...
[2025-05-25 19:14:45] [INFO] eco-edge-network already exists
[2025-05-25 19:14:45] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER b6856a0b93cb  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 19:14:45] [INFO] Checking for existing containers...
[2025-05-25 19:14:45] [INFO] Found existing container: octopus-service
[2025-05-25 19:14:46] [INFO] Stopping container: octopus-service
[2025-05-25 19:14:54] [INFO] Stop container output: octopus-service
[2025-05-25 19:14:54] [INFO] Removing container: octopus-service
[2025-05-25 19:14:54] [INFO] Remove container output: octopus-service
[2025-05-25 19:14:54] [INFO] Found existing container: ewp-edge
[2025-05-25 19:14:55] [INFO] Stopping container: ewp-edge
[2025-05-25 19:14:57] [INFO] Stop container output: ewp-edge
[2025-05-25 19:14:57] [INFO] Removing container: ewp-edge
[2025-05-25 19:14:57] [INFO] Remove container output: ewp-edge
[2025-05-25 19:14:57] [INFO] Running octopus-service container...
[2025-05-25 19:14:57] [INFO] Added backend environment arguments: -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e REST_PORT=9080 -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e REST_HOST=0.0.0.0 -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db -e DB_PATH=/app/db -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem
[2025-05-25 19:14:57] [INFO] Added volume arguments: -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\db:/app/db -v C:\data1\backend\certs:/app/certs
[2025-05-25 19:14:57] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -p 0.0.0.0:9080:9080 -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e AUTH_DB_PATH=/app/db/auth/auth.db -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e REST_PORT=9080 -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e REST_HOST=0.0.0.0 -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db -e DB_PATH=/app/db -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\db:/app/db -v C:\data1\backend\certs:/app/certs --user 1000:1000 octopus-service:latest
[2025-05-25 19:14:59] [INFO] Container start output: 790310b98cd7089297a25e5e3103c54d44fba525f966ebaa9122ff2220b77f58
[2025-05-25 19:15:02] [INFO] octopus-service container started successfully
[2025-05-25 19:15:02] [INFO] Backend service running on port 9080
[2025-05-25 19:15:02] [INFO] Running ewp-edge container...
[2025-05-25 19:15:02] [INFO] Added frontend environment arguments: -e API_URL=http://locolhost:9080 -e NGINX_PORT=80
[2025-05-25 19:15:02] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -p 0.0.0.0:8080:80 -e API_URL=http://locolhost:9080 -e NGINX_PORT=80 ewp-edge:latest
[2025-05-25 19:15:03] [INFO] Container start output: 29c04f439013d150f65ca3139db9f13cc31dc4a126e8c526cc103ab6c06573d0
[2025-05-25 19:15:05] [INFO] ewp-edge container started successfully
[2025-05-25 19:15:05] [INFO] Frontend service running on port 8080
[2025-05-25 19:15:05] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 19:15:05] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 19:15:05] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 19:15:05] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 19:15:05] [INFO] Found 30 files in source directory
[2025-05-25 19:15:05] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 19:15:05] [INFO] Checking for locked files in source directory...
[2025-05-25 19:15:06] [INFO] Using robocopy for more reliable file copying
[2025-05-25 19:15:06] [INFO] Files copied successfully
[2025-05-25 19:15:06] [INFO] Setting up startup shortcut...
[2025-05-25 19:15:06] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 19:15:06] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 19:15:06] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 19:15:06] [INFO] Monitor executable found, creating shortcut
[2025-05-25 19:15:06] [INFO] Startup shortcut created successfully
[2025-05-25 19:15:06] [INFO] No existing Eco Edge Monitor processes found
[2025-05-25 19:15:06] [INFO] Checking for locked files in C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 19:15:06] [INFO] No locked files detected
[2025-05-25 19:15:06] [INFO] Starting Eco Edge Monitor application
[2025-05-25 19:15:06] [INFO] Started Eco Edge Monitor with process ID: 6932
[2025-05-25 19:15:06] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 19:15:08] [INFO] Verified Eco Edge Monitor is running with process ID: 6932
[2025-05-25 19:15:08] [INFO] Eco Edge Agent installation complete.
**********************
PowerShell transcript end
End time: 20250525191508
**********************
