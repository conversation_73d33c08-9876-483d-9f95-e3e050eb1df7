enum ServiceState {
  running,
  stopped,
  warning,
  unknown
}

class ServiceStatus {
  final String name;
  final ServiceState state;
  final String port;
  final String containerName;
  final String containerImage;
  final String containerID;
  final double cpuUsage;
  final double memoryUsage;

  ServiceStatus({
    required this.name,
    required this.state,
    required this.port,
    this.containerName = '',
    this.containerImage = '',
    this.containerID = '',
    this.cpuUsage = 0.0,
    this.memoryUsage = 0.0,
  });

  ServiceStatus copyWith({
    String? name,
    ServiceState? state,
    String? port,
    String? containerName,
    String? containerImage,
    String? containerID,
    double? cpuUsage,
    double? memoryUsage,
  }) {
    return ServiceStatus(
      name: name ?? this.name,
      state: state ?? this.state,
      port: port ?? this.port,
      containerName: containerName ?? this.containerName,
      containerImage: containerImage ?? this.containerImage,
      containerID: containerID ?? this.containerID,
      cpuUsage: cpuUsage ?? this.cpuUsage,
      memoryUsage: memoryUsage ?? this.memoryUsage,
    );
  }
}

class EcoEdgeStatus {
  final ServiceStatus frontendService;
  final ServiceStatus backendService;
  final DateTime lastUpdated;

  EcoEdgeStatus({
    required this.frontendService,
    required this.backendService,
    required this.lastUpdated,
  });

  ServiceState get overallState {
    if (frontendService.state == ServiceState.stopped || 
        backendService.state == ServiceState.stopped) {
      return ServiceState.stopped;
    } else if (frontendService.state == ServiceState.warning || 
               backendService.state == ServiceState.warning) {
      return ServiceState.warning;
    } else if (frontendService.state == ServiceState.running && 
               backendService.state == ServiceState.running) {
      return ServiceState.running;
    } else {
      return ServiceState.unknown;
    }
  }

  EcoEdgeStatus copyWith({
    ServiceStatus? frontendService,
    ServiceStatus? backendService,
    DateTime? lastUpdated,
  }) {
    return EcoEdgeStatus(
      frontendService: frontendService ?? this.frontendService,
      backendService: backendService ?? this.backendService,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
