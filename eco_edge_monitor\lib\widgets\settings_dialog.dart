import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/eco_edge_provider.dart';

class SettingsDialog extends StatefulWidget {
  const SettingsDialog({Key? key}) : super(key: key);

  @override
  _SettingsDialogState createState() => _SettingsDialogState();
}

class _SettingsDialogState extends State<SettingsDialog> {
  late int _refreshInterval;
  late bool _startMinimized;
  late bool _startOnBoot;

  @override
  void initState() {
    super.initState();
    final provider = Provider.of<EcoEdgeProvider>(context, listen: false);
    _refreshInterval = provider.refreshInterval;
    _startMinimized = provider.startMinimized;
    _startOnBoot = provider.startOnBoot;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Settings'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildRefreshIntervalSetting(),
            const SizedBox(height: 16),
            _buildStartMinimizedSetting(),
            const SizedBox(height: 8),
            _buildStartOnBootSetting(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveSettings,
          child: const Text('Save'),
        ),
      ],
    );
  }

  Widget _buildRefreshIntervalSetting() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Refresh Interval',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Slider(
          value: _refreshInterval.toDouble(),
          min: 1,
          max: 60,
          divisions: 59,
          label: '$_refreshInterval seconds',
          onChanged: (value) {
            setState(() {
              _refreshInterval = value.round();
            });
          },
        ),
        Text('Update status every $_refreshInterval seconds'),
      ],
    );
  }

  Widget _buildStartMinimizedSetting() {
    return CheckboxListTile(
      title: const Text('Start Minimized'),
      subtitle: const Text('Start the application minimized to system tray'),
      value: _startMinimized,
      onChanged: (value) {
        setState(() {
          _startMinimized = value ?? false;
        });
      },
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildStartOnBootSetting() {
    return CheckboxListTile(
      title: const Text('Start on Boot'),
      subtitle: const Text('Start the application when Windows starts'),
      value: _startOnBoot,
      onChanged: (value) {
        setState(() {
          _startOnBoot = value ?? false;
        });
      },
      contentPadding: EdgeInsets.zero,
    );
  }

  void _saveSettings() {
    final provider = Provider.of<EcoEdgeProvider>(context, listen: false);
    provider.saveSettings(
      refreshInterval: _refreshInterval,
      startMinimized: _startMinimized,
      startOnBoot: _startOnBoot,
    );
    Navigator.of(context).pop();
  }
}
