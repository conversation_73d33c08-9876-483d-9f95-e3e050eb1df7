import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:window_manager/window_manager.dart';
import 'package:tray_manager/tray_manager.dart';

import 'models/service_status.dart';
import 'providers/eco_edge_provider.dart';
import 'screens/home_screen.dart';
import 'services/system_tray_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize tray manager first
  // No need to initialize tray manager separately

  // Initialize window manager
  await windowManager.ensureInitialized();

  WindowOptions windowOptions = const WindowOptions(
    size: Size(800, 600),
    center: true,
    backgroundColor: Colors.transparent,
    skipTaskbar: false,
    titleBarStyle: TitleBarStyle.normal,
    title: 'Eco Edge Monitor',
    minimumSize: Size(400, 300),
  );

  await windowManager.setPreventClose(true);

  await windowManager.waitUntilReadyToShow(windowOptions, () async {
    // Show window after a short delay to ensure tray is initialized
    await Future.delayed(Duration(milliseconds: 500));
    await windowManager.show();
    await windowManager.focus();
  });

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WindowListener {
  final SystemTrayManager _systemTrayManager = SystemTrayManager();
  late EcoEdgeProvider _ecoEdgeProvider;

  @override
  void initState() {
    super.initState();
    windowManager.addListener(this);
    _ecoEdgeProvider = EcoEdgeProvider();
    _initSystemTray();
  }

  @override
  void dispose() {
    windowManager.removeListener(this);
    _systemTrayManager.dispose();
    super.dispose();
  }

  @override
  void onWindowClose() async {
    bool isPreventClose = await windowManager.isPreventClose();
    if (isPreventClose) {
      await windowManager.hide();
    }
  }

  @override
  void onWindowMinimize() async {
    // Hide the window instead of minimizing
    await windowManager.hide();
  }

  @override
  void onWindowFocus() {
    // Update status when window gets focus
    _ecoEdgeProvider.refreshStatus();
    _updateSystemTrayIcon();
  }

  Future<void> _initSystemTray() async {
    await _systemTrayManager.initSystemTray(
      refreshCallback: () async {
        await _ecoEdgeProvider.refreshStatus();
        _updateSystemTrayIcon();
      },
      startAllCallback: () async {
        await _ecoEdgeProvider.startAllServices();
        _updateSystemTrayIcon();
      },
      stopAllCallback: () async {
        await _ecoEdgeProvider.stopAllServices();
        _updateSystemTrayIcon();
      },
      restartAllCallback: () async {
        await _ecoEdgeProvider.restartAllServices();
        _updateSystemTrayIcon();
      },
      openSettingsCallback: () async {
        await windowManager.show();
        await windowManager.focus();
      },
      exitAppCallback: () {
        exit(0);
      },
    );

    // Initial update of system tray icon
    _updateSystemTrayIcon();
  }

  void _updateSystemTrayIcon() {
    final status = _ecoEdgeProvider.status;
    if (status != null) {
      _systemTrayManager.updateIcon(status.overallState);
      _systemTrayManager.updateToolTip(
        'Eco Edge Monitor - ${_getStatusText(status.overallState)}',
      );
    } else {
      _systemTrayManager.updateIcon(ServiceState.unknown);
      _systemTrayManager.updateToolTip('Eco Edge Monitor - Status unknown');
    }
  }

  String _getStatusText(ServiceState state) {
    switch (state) {
      case ServiceState.running:
        return 'All services running';
      case ServiceState.stopped:
        return 'Some services stopped';
      case ServiceState.warning:
        return 'Warning';
      case ServiceState.unknown:
      default:
        return 'Unknown';
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _ecoEdgeProvider,
      child: MaterialApp(
        title: 'Eco Edge Monitor',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.green),
          useMaterial3: true,
        ),
        home: const HomeScreen(),
      ),
    );
  }
}
