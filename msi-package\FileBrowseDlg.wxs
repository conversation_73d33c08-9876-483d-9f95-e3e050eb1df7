<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
   <Fragment>
      <UI>
         <Dialog Id="FileBrowseDlg" Width="370" Height="270" Title="ESXP Edge Agent Setup">
            <!-- Banner -->
            <Control Id="BannerBitmap" Type="Bitmap" X="0" Y="0" Width="370" Height="44" TabSkip="no" Text="WixUI_Bmp_Banner" />
            <Control Id="BannerLine" Type="Line" X="0" Y="44" Width="370" Height="0" />
            <Control Id="BottomLine" Type="Line" X="0" Y="234" Width="370" Height="0" />

            <!-- Title and description -->
            <Control Id="Title" Type="Text" X="15" Y="6" Width="200" Height="15" Transparent="yes" NoPrefix="yes" Text="{\WixUI_Font_Title}Configuration Settings" />
            <Control Id="Description" Type="Text" X="25" Y="23" Width="280" Height="15" Transparent="yes" NoPrefix="yes" Text="Customize deployment parameters or proceed with standard settings" />

            <!-- File selection controls -->
            <Control Id="FileLabel" Type="Text" X="20" Y="60" Width="290" Height="30" NoPrefix="yes" Text="Specify a custom configuration file (optional):" />
            <Control Id="OptionalNote" Type="Text" X="20" Y="80" Width="290" Height="18" NoPrefix="yes" Text="The system will use standard configuration parameters if none are provided." />
            <Control Id="FilePath" Type="Edit" X="20" Y="120" Width="260" Height="18" Property="SELECTEDFILEPATH" />
            <Control Id="BrowseButton" Type="PushButton" X="285" Y="120" Width="56" Height="18" Text="Browse..." />

            <!-- Navigation buttons -->
            <Control Id="Back" Type="PushButton" X="180" Y="243" Width="56" Height="17" Text="Back" />
            <Control Id="Next" Type="PushButton" X="236" Y="243" Width="56" Height="17" Default="yes" Text="Next" />
            <Control Id="Cancel" Type="PushButton" X="304" Y="243" Width="56" Height="17" Cancel="yes" Text="Cancel">
               <Publish Event="SpawnDialog" Value="CancelDlg">1</Publish>
            </Control>
         </Dialog>

         <!-- Use the custom action for file browsing -->
         <!-- When the Browse button is clicked, call the BrowseForFile custom action -->
         <Publish Dialog="FileBrowseDlg" Control="BrowseButton" Event="DoAction" Value="BrowseForFile">1</Publish>
         
         <!-- After the BrowseForFile action completes, update the FilePath control -->
         <Publish Dialog="FileBrowseDlg" Control="BrowseButton" Property="SELECTEDFILEPATH" Value="[SELECTEDFILEPATH]" Order="2">1</Publish>
      </UI>
   </Fragment>
</Wix>
