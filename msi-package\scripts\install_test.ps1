# install_test.ps1
# Test script to verify that the custom action is working correctly

# Create a log file to track execution
$logFile = Join-Path -Path $env:TEMP -ChildPath "eco_edge_install_log.txt"

# Function to log messages
function Write-Log {
    param (
        [string]$Message
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "$timestamp - $Message" | Out-File -FilePath $logFile -Append
}

# Log the start of the script
Write-Log "Starting installation script"
Write-Log "Script is running from: $PSScriptRoot"

# Log environment variables
Write-Log "INSTALLDIR: $env:INSTALLDIR"
Write-Log "Current directory: $(Get-Location)"
Write-Log "Current user: $env:USERNAME"
Write-Log "Is admin: $([bool](([System.Security.Principal.WindowsIdentity]::GetCurrent()).groups -match 'S-1-5-32-544'))"

# Try to create a test file in the installation directory
try {
    $installDir = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
    Write-Log "Determined install directory: $installDir"

    # Check if we have write permissions to the install directory
    $testFile = Join-Path -Path $installDir -ChildPath "install_test.txt"
    try {
        # Test if we can write to the directory without admin privileges
        "Installation test successful at $(Get-Date)" | Out-File -FilePath $testFile -ErrorAction Stop
        Write-Log "Created test file at: $testFile"
    } catch {
        Write-Log "Cannot write directly to install directory, using user's temp folder instead"
        $testFile = Join-Path -Path $env:TEMP -ChildPath "eco_edge_install_test.txt"
        "Installation test successful at $(Get-Date)" | Out-File -FilePath $testFile
        Write-Log "Created test file at: $testFile (in temp folder)"
    }
} catch {
    Write-Log "Error during installation test: $_"
    # Don't exit with error code as this might not be critical
    Write-Log "Continuing installation despite test file error"
}

# Log successful completion
Write-Log "Installation script completed successfully"
exit 0
