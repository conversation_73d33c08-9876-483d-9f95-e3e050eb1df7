import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:tray_manager/tray_manager.dart';
import 'package:window_manager/window_manager.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/service_status.dart';

class SystemTrayManager with TrayListener {
  final WindowManager _windowManager = WindowManager.instance;

  // Store callbacks for menu actions
  late Function _refreshCallback;
  late Function _startAllCallback;
  late Function _stopAllCallback;
  late Function _restartAllCallback;
  late Function _openSettingsCallback;
  late Function _exitAppCallback;

  Future<void> initSystemTray({
    required Function refreshCallback,
    required Function startAllCallback,
    required Function stopAllCallback,
    required Function restartAllCallback,
    required Function openSettingsCallback,
    required Function exitAppCallback,
  }) async {
    // Store callbacks
    _refreshCallback = refreshCallback;
    _startAllCallback = startAllCallback;
    _stopAllCallback = stopAllCallback;
    _restartAllCallback = restartAllCallback;
    _openSettingsCallback = openSettingsCallback;
    _exitAppCallback = exitAppCallback;

    // Add tray listener
    trayManager.addListener(this);

    // Set tray icon - use the app icon from the Windows executable
    await trayManager.setIcon(
      'assets/icons/app_icon.png',
      // Platform.isWindows ? 'app_icon.ico' : 'assets/icons/app_icon.png',
    );

    // Set tooltip
    await trayManager.setToolTip('Eco Edge Monitor');

    // Create menu
    await _buildMenu();
  }

  Future<void> _buildMenu() async {
    final menu = Menu(
      items: [
        MenuItem(label: 'Eco Edge Monitor', disabled: true),
        MenuItem.separator(),
        MenuItem(label: 'Show Window'),
        MenuItem(label: 'Refresh Status'),
        MenuItem.separator(),
        MenuItem(label: 'Start All Services'),
        MenuItem(label: 'Stop All Services'),
        MenuItem(label: 'Restart All Services'),
        MenuItem.separator(),
        MenuItem(label: 'Open Frontend (Browser)'),
        MenuItem(label: 'Open Backend API (Browser)'),
        MenuItem.separator(),
        MenuItem(label: 'Settings'),
        MenuItem.separator(),
        MenuItem(label: 'Exit'),
      ],
    );

    await trayManager.setContextMenu(menu);
  }

  // TrayListener methods
  @override
  void onTrayIconMouseDown() {
    // Show the window when the tray icon is clicked
    _windowManager.show();
  }

  @override
  void onTrayIconRightMouseDown() {
    // Show the context menu when right-clicked
    trayManager.popUpContextMenu();
  }

  @override
  void onTrayMenuItemClick(MenuItem menuItem) async {
    print('Menu item clicked: ${menuItem.label}');

    switch (menuItem.label) {
      case 'Show Window':
        await _windowManager.show();
        await _windowManager.focus();
        break;
      case 'Refresh Status':
        await _refreshCallback();
        break;
      case 'Start All Services':
        await _startAllCallback();
        break;
      case 'Stop All Services':
        await _stopAllCallback();
        break;
      case 'Restart All Services':
        await _restartAllCallback();
        break;
      case 'Open Frontend (Browser)':
        final frontendPort = await _getInstalledPort('FrontendPort', '8080');
        await launchUrl(Uri.parse('http://localhost:$frontendPort'));
        break;
      case 'Open Backend API (Browser)':
        final backendPort = await _getInstalledPort('BackendPort', '3000');
        await launchUrl(Uri.parse('http://localhost:$backendPort/api/hello'));
        break;
      case 'Settings':
        await _openSettingsCallback();
        break;
      case 'Exit':
        await _exitAppCallback();
        break;
    }
  }

  Future<void> updateIcon(ServiceState state) async {
    String iconPath;

    if (Platform.isWindows) {
      // On Windows, use ICO files from the Windows resources directory
      final String baseIconPath =
          'C:/vinayak_pc/ESXP-Edge-Agent/eco-agent/eco_edge_monitor/windows/runner/resources';
      switch (state) {
        case ServiceState.running:
          iconPath =
              '$baseIconPath/app_icon.ico'; // Using the same icon for now
          break;
        case ServiceState.stopped:
          iconPath =
              '$baseIconPath/app_icon.ico'; // Using the same icon for now
          break;
        case ServiceState.warning:
          iconPath =
              '$baseIconPath/app_icon.ico'; // Using the same icon for now
          break;
        case ServiceState.unknown:
        default:
          iconPath = '$baseIconPath/app_icon.ico';
          break;
      }
    } else {
      // On other platforms, use PNG files from assets
      switch (state) {
        case ServiceState.running:
          iconPath = 'assets/icons/app_icon_running.png';
          break;
        case ServiceState.stopped:
          iconPath = 'assets/icons/app_icon_stopped.png';
          break;
        case ServiceState.warning:
          iconPath = 'assets/icons/app_icon_warning.png';
          break;
        case ServiceState.unknown:
        default:
          iconPath = 'assets/icons/app_icon.png';
          break;
      }
    }

    await trayManager.setIcon(iconPath);
  }

  Future<void> updateToolTip(String message) async {
    await trayManager.setToolTip(message);
  }

  Future<String> _getInstalledPort(String portType, String defaultPort) async {
    try {
      // Check if we're running as admin
      final isAdmin =
          Platform.isWindows &&
          await Process.run('powershell', [
            '-Command',
            '[bool]([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)',
          ]).then((result) => result.stdout.trim() == 'True');

      // Determine installation directory
      final installDir =
          isAdmin
              ? '${Platform.environment['ProgramData']}\\eco-edge'
              : '${Platform.environment['LOCALAPPDATA']}\\eco-edge';

      final installInfoPath = '$installDir\\install-info.json';
      final file = File(installInfoPath);

      if (await file.exists()) {
        final content = await file.readAsString();
        final json = jsonDecode(content);
        return json[portType]?.toString() ?? defaultPort;
      }
    } catch (e) {
      print('Error reading installation info: $e');
    }

    return defaultPort;
  }

  void dispose() {
    trayManager.removeListener(this);
  }
}
