# WiX v3.x Installer with UI for Installation Directory and File Selection

This project contains WiX v3.x source files for creating an MSI installer with:
1. A UI for selecting the installation directory
2. A UI with a browse button for selecting a file

## Files Included

- `Product.wxs` - Main WiX file defining the product, features, and components
- `WixUI_Custom.wxs` - Custom UI sequence that includes both installation directory and file selection dialogs
- `FileBrowseDlg.wxs` - Definition of the file selection dialog with browse button
- `FileBrowseCA.cpp` - C++ source code for the custom action that handles file browsing
- `FileBrowseCA.def` - Export definition file for the custom action DLL
- `license.rtf` - Sample license agreement in RTF format

## Requirements

- WiX Toolset v3.x
- Visual Studio (for compiling the custom action DLL)

## Building the Custom Action DLL

1. Create a C++ DLL project in Visual Studio
2. Add the `FileBrowseCA.cpp` and `FileBrowseCA.def` files to the project
3. Add references to the WiX SDK (typically located in `C:\Program Files (x86)\WiX Toolset v3.x\SDK`)
4. Build the project to create `FileBrowse.dll`

## Building the MSI

1. Create a WiX project in Visual Studio
2. Add all the .wxs files to the project
3. Add a reference to the WixUIExtension.dll
4. Place the custom action DLL (`FileBrowse.dll`) in the same directory as your WiX project
5. Create or obtain banner.bmp and dialog.bmp files for the UI
6. Build the project to create the MSI

## Customization

- Update the product information in `Product.wxs`
- Modify the file filter in `FileBrowseCA.cpp` to limit the types of files that can be selected
- Customize the UI text and layout in the .wxs files

## Notes

- The custom action DLL requires the WiX Custom Action framework
- The file browsing functionality uses the Windows common dialog API
- This example assumes you have a valid license.rtf file for the license agreement
