<?xml version="1.0" encoding="utf-8"?>
<Wix xmlns="http://wixtoolset.org/schemas/v4/wxs"
     xmlns:ui="http://wixtoolset.org/schemas/v4/wxs/ui">
  <Package Name="Eco Edge Agent" 
           Manufacturer="YourCompany"
           Version="1.0.0.0"
           UpgradeCode="D2B5C5A2-7F7B-4E3B-8D0A-1F8B6B2D2C1E">
    
    <!-- Define properties -->
    <Property Id="CONFIGFILEPATH" Secure="yes" />
    <Property Id="WIXUI_INSTALLDIR" Value="INSTALLFOLDER" />
    
    <!-- Define directory structure -->
    <StandardDirectory Id="ProgramFilesFolder">
      <Directory Id="INSTALLFOLDER" Name="EcoEdgeAgent" />
    </StandardDirectory>

    <!-- Define components -->
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component>
        <File Source="..\scripts\install.ps1" Id="InstallPs1" />
      </Component>
    </ComponentGroup>

    <!-- Define features -->
    <Feature Id="ProductFeature" Title="Eco Edge Agent" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
    </Feature>

    <!-- Define UI -->
    <ui:WixUI Id="WixUI_InstallDir" />
    
    <!-- Command line to pass config file path to install.ps1 -->
    <CustomAction Id="RunInstallScript"
                  FileRef="InstallPs1"
                  ExeCommand="powershell.exe -ExecutionPolicy Bypass -File &quot;[INSTALLFOLDER]install.ps1&quot; -ConfigFilePath &quot;[CONFIGFILEPATH]&quot;"
                  Execute="deferred"
                  Return="check"
                  Impersonate="no" />
                  
    <InstallExecuteSequence>
      <Custom Action="RunInstallScript" After="InstallFiles" Condition="NOT Installed" />
    </InstallExecuteSequence>
  </Package>
</Wix>
