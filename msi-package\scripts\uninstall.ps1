# PowerShell uninstallation script for Eco Edge Agent (Windows)
# Following the specified steps:
# 1. Remove the startup link
# 2. Kill and remove the Flutter application
# 3. Stop and remove all containers
# 4. Remove all images
# 5. Remove all data and configuration files
# 6. Remove all registry entries and startup configurations

# Create a log file to track execution
$logFile = Join-Path -Path $env:TEMP -ChildPath "eco_edge_uninstall_log.txt"

# Function to log messages
function Write-Log {
    param (
        [string]$Message
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "$timestamp - $Message" | Out-File -FilePath $logFile -Append
    Write-Host "$timestamp - $Message"
}

# Function to check if running as administrator
function Test-Admin {
    $currentUser = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    $isAdmin = $currentUser.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    return $isAdmin
}

# Log the start of the script
Write-Log "Starting Eco Edge Agent uninstallation..."
Write-Log "Script is running from: $PSScriptRoot"
Write-Log "Current user: $env:USERNAME"
$isAdmin = Test-Admin
Write-Log "Is admin: $isAdmin"

# When running from MSI, we're already elevated, so skip this check
# If running standalone, check for admin rights
if (-not $isAdmin -and -not $env:INSTALLDIR) {
    Write-Log "Not running as administrator. Attempting to restart with elevation..."
    try {
        $scriptPath = $MyInvocation.MyCommand.Path
        Start-Process powershell.exe -ArgumentList "-NoProfile -ExecutionPolicy Bypass -File `"$scriptPath`"" -Verb RunAs
        Write-Log "Elevated process started. Exiting current non-elevated process."
        exit 0 # Exit with success code
    }
    catch {
        Write-Log "Failed to restart with elevation: $_"
        Write-Log "Continuing without elevation, but some operations may fail."
    }
}

# Determine the installation directory
# When running from MSI, INSTALLDIR environment variable should be set
if ($env:INSTALLDIR -and (Test-Path $env:INSTALLDIR)) {
    $installRoot = $env:INSTALLDIR
    Write-Log "Using installation directory from MSI: $installRoot"
}
else {
    # First try to get it from the script location
    $installRoot = Split-Path -Parent $PSScriptRoot
    Write-Log "Trying script parent directory: $installRoot"
    
    # If that doesn't work, try to find it in common locations
    if (-not (Test-Path $installRoot)) {
        Write-Log "Installation root not found at script parent directory. Trying alternative methods..."

        # Try LocalAppData location (where we install by default)
        $possibleRoot = Join-Path $env:LOCALAPPDATA "EcoEdgeAgent"
        if (Test-Path $possibleRoot) {
            $installRoot = $possibleRoot
            Write-Log "Found installation root in LocalAppData: $installRoot"
        }
        else {
            # Try ProgramData location
            $possibleRoot = Join-Path $env:ProgramData "EcoEdgeAgent"
            if (Test-Path $possibleRoot) {
                $installRoot = $possibleRoot
                Write-Log "Found installation root in ProgramData: $installRoot"
            }
            else {
                # Try Program Files location
                $possibleRoot = Join-Path $env:ProgramFiles "EcoEdgeAgent"
            if (Test-Path $possibleRoot) {
                $installRoot = $possibleRoot
                Write-Log "Found installation root in Program Files (x86): $installRoot"
            }
            else {
                $possibleRoot = Join-Path $env:ProgramFiles "EcoEdgeAgent"
                if (Test-Path $possibleRoot) {
                    $installRoot = $possibleRoot
                    Write-Log "Found installation root in Program Files: $installRoot"
                }
                else {
                    Write-Log "WARNING: Could not determine installation directory. Using script parent as fallback."
                }
            }
        }
    }
}
Write-Log "Using installation root directory: $installRoot"

Write-Log "Following the specified uninstallation steps..."

# Step 1: Remove the startup link
Write-Log "Step 1: Removing startup links..."
$startupPath = [Environment]::GetFolderPath("Startup")
$startupShortcutFile = Join-Path $startupPath "Eco Edge Monitor.lnk"
if (Test-Path $startupShortcutFile) {
    Write-Log "Found startup shortcut: $startupShortcutFile"
    try {
        Remove-Item -Path $startupShortcutFile -Force -ErrorAction Stop
        Write-Log "Startup shortcut removed successfully."
    }
    catch {
        Write-Log "Error removing startup shortcut: $_"
    }
} else {
    Write-Log "No startup shortcut found at: $startupShortcutFile"
}

# Check for any other shortcuts in the startup folder that might be related
try {
    $relatedShortcuts = Get-ChildItem -Path $startupPath -Filter "*eco*edge*.lnk" -ErrorAction SilentlyContinue
    foreach ($shortcut in $relatedShortcuts) {
        Write-Log "Found related startup shortcut: $($shortcut.FullName)"
        try {
            Remove-Item -Path $shortcut.FullName -Force -ErrorAction Stop
            Write-Log "Removed related startup shortcut: $($shortcut.FullName)"
        }
        catch {
            Write-Log "Error removing related startup shortcut: $_"
        }
    }
}
catch {
    Write-Log "Error searching for related startup shortcuts: $_"
}

# Step 2: Kill and remove the Flutter application
Write-Log "Step 2: Killing and removing the Flutter application..."

# List all running processes to help with debugging
try {
    Write-Log "Listing all running processes..."
    $allProcesses = Get-Process | Where-Object { $_.Name -like "*eco*" -or $_.Name -like "*edge*" -or $_.Name -like "*monitor*" } | Select-Object Name, Id, Path
    if ($allProcesses) {
        foreach ($proc in $allProcesses) {
            Write-Log "Found process: $($proc.Name) (ID: $($proc.Id), Path: $($proc.Path))"
        }
    }
    else {
        Write-Log "No relevant processes found."
    }
}
catch {
    Write-Log "Error listing processes: $_"
}

# Kill any running instances of the application - try multiple possible process names
$processNames = @("eco_edge_monitor", "ecoedgemonitor", "eco-edge-monitor", "ecoedge")
foreach ($procName in $processNames) {
    try {
        Write-Log "Looking for process: $procName"
        $processes = Get-Process -Name $procName -ErrorAction SilentlyContinue
        if ($processes) {
            Write-Log "Found $($processes.Count) instances of $procName"
            $processes | ForEach-Object {
                Write-Log "Killing process ID: $($_.Id)"
                $_.Kill()
            }
            Write-Log "Stopped $procName processes."
            Start-Sleep -Seconds 2 # Give it time to fully terminate
        }
        else {
            Write-Log "No running $procName processes found."
        }
    }
    catch {
        Write-Log "Error stopping $procName processes: $_"
    }
}

# Remove the Flutter application files
try {
    $monitorDir = Join-Path $installRoot "monitor"
    $ecoEdgeDir = Join-Path $installRoot "eco-edge"

    # Check if the monitor directory exists and remove it
    if (Test-Path $monitorDir) {
        Write-Log "Removing Flutter application from monitor directory: $monitorDir"
        try {
            # List files in the directory before removing
            $files = Get-ChildItem -Path $monitorDir -Recurse
            Write-Log "Found $($files.Count) files/directories in monitor directory"

            # Try to remove the directory
            Remove-Item -Path $monitorDir -Recurse -Force -ErrorAction Stop
            Write-Log "Successfully removed Flutter application from monitor directory"
        }
        catch {
            Write-Log "Error removing monitor directory: $_"
        }
    }
    else {
        Write-Log "Monitor directory not found: $monitorDir"
    }

    # Check if the eco-edge directory exists and remove it
    if (Test-Path $ecoEdgeDir) {
        Write-Log "Removing Flutter application from eco-edge directory: $ecoEdgeDir"
        try {
            # List files in the directory before removing
            $files = Get-ChildItem -Path $ecoEdgeDir -Recurse
            Write-Log "Found $($files.Count) files/directories in eco-edge directory"

            # Try to remove the directory
            Remove-Item -Path $ecoEdgeDir -Recurse -Force -ErrorAction Stop
            Write-Log "Successfully removed Flutter application from eco-edge directory"
        }
        catch {
            Write-Log "Error removing eco-edge directory: $_"
        }
    }
    else {
        Write-Log "Eco-edge directory not found: $ecoEdgeDir"
    }
}
catch {
    Write-Log "Error removing Flutter application: $_"
}

# Step 3: Stop and remove all containers
Write-Log "Step 3: Stopping and removing all containers..."

# Check if podman is installed and accessible
try {
    $podmanInstalled = Get-Command podman -ErrorAction Stop
    Write-Log "Podman is installed at: $($podmanInstalled.Source)"

    # Test if podman is working
    $podmanVersion = podman version 2>&1
    Write-Log "Podman version: $podmanVersion"

    # List all containers first to see what's available
    try {
        Write-Log "Listing all containers..."
        $allContainers = podman ps -a 2>&1
        Write-Log "Current containers: $allContainers"
    }
    catch {
        Write-Log "Error listing containers: $_"
    }

    # First, try to stop and remove specific containers
    $containers = @("octopus-service", "ewp-edge")
    foreach ($container in $containers) {
        try {
            Write-Log "Stopping container: $container"
            podman stop $container 2>&1 | Out-Null
            Write-Log "Removing container: $container"
            podman rm -f $container 2>&1 | Out-Null
        }
        catch {
            Write-Log "Error processing container $container: $_"
        }
    }

    # Then, try to stop and remove all containers
    try {
        Write-Log "Stopping all containers..."
        $runningContainers = @(podman ps -q)
        if ($runningContainers.Count -gt 0) {
            Write-Log "Found $($runningContainers.Count) running containers"
            podman stop $runningContainers 2>&1 | Out-Null
        }

        Write-Log "Removing all containers..."
        $allContainerIds = @(podman ps -a -q)
        if ($allContainerIds.Count -gt 0) {
            Write-Log "Found $($allContainerIds.Count) containers to remove"
            podman rm -f $allContainerIds 2>&1 | Out-Null
        }
    }
    catch {
        Write-Log "Error removing all containers: $_"
    }

    # Remove eco-edge-network if it exists
    try {
        Write-Log "Removing eco-edge-network..."
        podman network rm eco-edge-network 2>&1 | Out-Null
    }
    catch {
        Write-Log "Error removing network: $_"
    }
}
catch {
    Write-Log "Podman is not installed or not accessible: $_"
    Write-Log "Skipping container cleanup."
}

# Step 4: Remove all images
Write-Log "Step 4: Removing all container images..."

# Check if podman is installed
try {
    # First, list all images to see what's available
    Write-Log "Listing all container images..."
    $imageList = podman images 2>&1
    Write-Log "Current images: $imageList"

    # First, try to remove specific images
    $images = @("octopus-service", "ewp-edge")
    foreach ($image in $images) {
        try {
            Write-Log "Removing image: $image"
            podman rmi -f $image 2>&1 | Out-Null
        }
        catch {
            Write-Log "Error removing image $image: $_"
        }
    }

    # Then, try to remove all images
    try {
        Write-Log "Removing all images..."
        $allImageIds = @(podman images -q)
        if ($allImageIds.Count -gt 0) {
            Write-Log "Found $($allImageIds.Count) images to remove"
            podman rmi -f $allImageIds 2>&1 | Out-Null
        }
    }
    catch {
        Write-Log "Error removing all images: $_"
    }

    # Finally, prune any remaining images
    try {
        Write-Log "Pruning unused images..."
        podman image prune -f 2>&1 | Out-Null
    }
    catch {
        Write-Log "Error pruning unused images: $_"
    }
}
catch {
    Write-Log "Error during image cleanup: $_"
}

# Check again for any remaining processes
Write-Log "Checking for any remaining processes..."

# Try to kill any processes that might have been missed or restarted
try {
    $remainingProcesses = Get-Process | Where-Object { $_.Name -like "*eco*" -or $_.Name -like "*edge*" -or $_.Name -like "*monitor*" } | Select-Object Name, Id, Path
    if ($remainingProcesses) {
        Write-Log "Found $($remainingProcesses.Count) remaining processes that might be related to the application"
        foreach ($proc in $remainingProcesses) {
            Write-Log "Attempting to kill process: $($proc.Name) (ID: $($proc.Id))"
            try {
                $proc.Kill()
                Write-Log "Successfully killed process: $($proc.Name) (ID: $($proc.Id))"
            }
            catch {
                Write-Log "Failed to kill process $($proc.Name) (ID: $($proc.Id)): $_"
            }
        }
    }
    else {
        Write-Log "No remaining processes found."
    }
}
catch {
    Write-Log "Error checking for remaining processes: $_"
}

# Remove the Flutter application files
try {
    # Use the already determined installation directory
    Write-Log "Using previously determined installation root directory: $installRoot"

    # List all directories in the installation root to help with debugging
    try {
        $allDirs = Get-ChildItem -Path $installRoot -Directory -ErrorAction SilentlyContinue
        if ($allDirs) {
            Write-Log "Directories in installation root: $($allDirs.Name -join ', ')"
        }
        else {
            Write-Log "No directories found in installation root."
        }
    }
    catch {
        Write-Log "Error listing directories in installation root: $_"
    }

    $monitorDir = Join-Path $installRoot "monitor"
    $ecoEdgeDir = Join-Path $installRoot "eco-edge"

    # Check if the monitor directory exists and remove it
    if (Test-Path $monitorDir) {
        Write-Log "Removing Flutter application from monitor directory: $monitorDir"
        try {
            # List files in the directory before removing
            $files = Get-ChildItem -Path $monitorDir -Recurse
            Write-Log "Found $($files.Count) files/directories in monitor directory"

            # Try to remove the directory
            Remove-Item -Path $monitorDir -Recurse -Force -ErrorAction Stop
            Write-Log "Successfully removed Flutter application from monitor directory"
        }
        catch {
            Write-Log "Error removing monitor directory: $_"

            # Try to remove files one by one
            try {
                Write-Log "Trying to remove files one by one..."
                $files | ForEach-Object {
                    try {
                        Remove-Item -Path $_.FullName -Force -ErrorAction SilentlyContinue
                        Write-Log "Removed: $($_.FullName)"
                    }
                    catch {
                        Write-Log "Failed to remove: $($_.FullName) - $_"
                    }
                }
            }
            catch {
                Write-Log "Error during individual file removal: $_"
            }
        }
    }
    else {
        Write-Log "Monitor directory not found: $monitorDir"
    }

    # Check if the eco-edge directory exists and remove it
    if (Test-Path $ecoEdgeDir) {
        Write-Log "Removing Flutter application from eco-edge directory: $ecoEdgeDir"
        try {
            # List files in the directory before removing
            $files = Get-ChildItem -Path $ecoEdgeDir -Recurse
            Write-Log "Found $($files.Count) files/directories in eco-edge directory"

            # Try to remove the directory
            Remove-Item -Path $ecoEdgeDir -Recurse -Force -ErrorAction Stop
            Write-Log "Successfully removed Flutter application from eco-edge directory"
        }
        catch {
            Write-Log "Error removing eco-edge directory: $_"

            # Try to remove files one by one
            try {
                Write-Log "Trying to remove files one by one..."
                $files | ForEach-Object {
                    try {
                        Remove-Item -Path $_.FullName -Force -ErrorAction SilentlyContinue
                        Write-Log "Removed: $($_.FullName)"
                    }
                    catch {
                        Write-Log "Failed to remove: $($_.FullName) - $_"
                    }
                }
            }
            catch {
                Write-Log "Error during individual file removal: $_"
            }
        }
    }
    else {
        Write-Log "Eco-edge directory not found: $ecoEdgeDir"
    }

    # Look for any other directories that might contain the Flutter application
    $otherDirs = Get-ChildItem -Path $installRoot -Directory | Where-Object { $_.Name -like "*eco*" -or $_.Name -like "*edge*" -or $_.Name -like "*flutter*" }
    foreach ($dir in $otherDirs) {
        if ($dir.FullName -ne $monitorDir -and $dir.FullName -ne $ecoEdgeDir -and $dir.FullName -ne $PSScriptRoot) {
            Write-Log "Found potential Flutter application directory: $($dir.FullName)"
            try {
                Remove-Item -Path $dir.FullName -Recurse -Force -ErrorAction Stop
                Write-Log "Successfully removed directory: $($dir.FullName)"
            }
            catch {
                Write-Log "Error removing directory $($dir.FullName): $_"
            }
        }
    }
}
catch {
    Write-Log "Error removing Flutter application: $_"
}

# Step 5: Remove all data and configuration files
Write-Log "Step 5: Removing all data and configuration files..."

# Use the already determined installation directory
$dataDir = Join-Path $installRoot "data"

if (Test-Path $dataDir) {
    Write-Log "Removing data directory: $dataDir"
    try {
        Remove-Item -Path $dataDir -Recurse -Force -ErrorAction Stop
        Write-Log "Uninstallation completed successfully."
        exit 0
    }
    catch {
        Write-Log "Error removing data directory: $_"
    }
}
else {
    Write-Log "Data directory not found: $dataDir"
}

# Step 6: Remove all registry entries and startup configurations
Write-Log "Step 6: Removing all registry entries and startup configurations..."

# 1. Remove startup shortcut if it exists
$startupPath = [Environment]::GetFolderPath("Startup")
$startupShortcutFile = Join-Path $startupPath "Eco Edge Monitor.lnk"
if (Test-Path $startupShortcutFile) {
    Write-Log "Removing startup shortcut..."
    try {
        Remove-Item -Path $startupShortcutFile -Force -ErrorAction Stop
        Write-Log "Startup shortcut removed successfully."
    }
    catch {
        Write-Log "Error removing startup shortcut: $_"
    }
}

# Check for any other shortcuts in the startup folder that might be related
try {
    $relatedShortcuts = Get-ChildItem -Path $startupPath -Filter "*eco*edge*.lnk" -ErrorAction SilentlyContinue
    foreach ($shortcut in $relatedShortcuts) {
        Write-Log "Found related startup shortcut: $($shortcut.FullName)"
        try {
            Remove-Item -Path $shortcut.FullName -Force -ErrorAction Stop
            Write-Log "Removed related startup shortcut: $($shortcut.FullName)"
        }
        catch {
            Write-Log "Error removing related startup shortcut: $_"
        }
    }
}
catch {
    Write-Log "Error searching for related startup shortcuts: $_"
}

# 2. Remove from Run registry key if present
try {
    $runKey = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run"
    if (Test-Path $runKey) {
        # Check for EcoEdgeMonitor entry
        $ecoEdgeValue = Get-ItemProperty -Path $runKey -Name "EcoEdgeMonitor" -ErrorAction SilentlyContinue
        if ($ecoEdgeValue) {
            Write-Log "Removing EcoEdgeMonitor from registry Run key..."
            Remove-ItemProperty -Path $runKey -Name "EcoEdgeMonitor" -Force
            Write-Log "Registry Run key entry removed successfully."
        }

        # Check for any other related entries
        $allRunEntries = Get-ItemProperty -Path $runKey -ErrorAction SilentlyContinue
        foreach ($entryName in $allRunEntries.PSObject.Properties.Name) {
            if ($entryName -like "*eco*edge*" -or $entryName -like "*edge*monitor*") {
                Write-Log "Found related registry Run key entry: $entryName"
                try {
                    Remove-ItemProperty -Path $runKey -Name $entryName -Force
                    Write-Log "Removed related registry Run key entry: $entryName"
                }
                catch {
                    Write-Log "Error removing related registry Run key entry: $_"
                }
            }
        }
    }
}
catch {
    Write-Log "Error removing registry Run key entries: $_"
}

# 3. Remove from Task Scheduler if present
try {
    # Check for EcoEdgeMonitor task
    $taskExists = Get-ScheduledTask -TaskName "EcoEdgeMonitor" -ErrorAction SilentlyContinue
    if ($taskExists) {
        Write-Log "Removing EcoEdgeMonitor scheduled task..."
        Unregister-ScheduledTask -TaskName "EcoEdgeMonitor" -Confirm:$false
        Write-Log "Scheduled task removed successfully."
    }

    # Check for any other related tasks
    $allTasks = Get-ScheduledTask -ErrorAction SilentlyContinue
    foreach ($task in $allTasks) {
        if ($task.TaskName -like "*eco*edge*" -or $task.TaskName -like "*edge*monitor*") {
            Write-Log "Found related scheduled task: $($task.TaskName)"
            try {
                Unregister-ScheduledTask -TaskName $task.TaskName -Confirm:$false
                Write-Log "Removed related scheduled task: $($task.TaskName)"
            }
            catch {
                Write-Log "Error removing related scheduled task: $_"
            }
        }
    }
}
catch {
    Write-Log "Error removing scheduled tasks: $_"
}

# 4. Clean up any remaining registry entries
Write-Log "Cleaning up registry entries..."
try {
    # Check for application registry keys
    $appKeys = @(
        "HKCU:\Software\EcoEdge",
        "HKCU:\Software\Eco Edge",
        "HKLM:\Software\EcoEdge",
        "HKLM:\Software\Eco Edge",
        "HKCU:\Software\Classes\eco-edge",
        "HKLM:\Software\Classes\eco-edge"
    )

    foreach ($keyPath in $appKeys) {
        if (Test-Path $keyPath) {
            Write-Log "Removing registry key: $keyPath"
            try {
                Remove-Item -Path $keyPath -Recurse -Force -ErrorAction Stop
                Write-Log "Successfully removed registry key: $keyPath"
            }
            catch {
                Write-Log "Error removing registry key $keyPath: $_"
            }
        }
    }
}
catch {
    Write-Log "Error cleaning up registry entries: $_"
}

# Set ErrorActionPreference to continue to prevent script from failing
$ErrorActionPreference = "Continue"

# Final cleanup of any remaining files in the installation directory
try {
    # Use the already determined installation directory

    # Get a list of all remaining files and directories in the installation directory
    $remainingItems = Get-ChildItem -Path $installRoot -Recurse -ErrorAction SilentlyContinue |
                     Where-Object { $_.FullName -notlike "*\scripts\*" } # Exclude the scripts directory since it's currently in use

    if ($remainingItems.Count -gt 0) {
        Write-Log "Found $($remainingItems.Count) remaining items in the installation directory"
        Write-Log "These will be removed by the MSI uninstaller after this script completes"
    }
    else {
        Write-Log "No remaining items found in the installation directory"
    }
}
catch {
    Write-Log "Error during final cleanup check: $_"
}

Write-Log "Eco Edge Agent uninstallation complete."

# Always exit with success code to prevent MSI uninstallation failure
exit 0
