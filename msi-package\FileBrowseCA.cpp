#include "stdafx.h"
#include <windows.h>
#include <msiquery.h>
#include <commdlg.h>
#include <strsafe.h>
#include <wcautil.h>


// Add this utility function here
void ReleaseStr(LPWSTR& str)
{
    if (str)
    {
        ::HeapFree(GetProcessHeap(), 0, str);
        str = nullptr;
    }
}

// Custom action for browsing for a file
UINT __stdcall BrowseForFile(MSIHANDLE hInstall)
{
    HRESULT hr = S_OK;
    UINT er = ERROR_SUCCESS;
    
    // File name selection variables
    OPENFILENAME ofn;
    LPWSTR szFilePath;
    wchar_t szFoundFileName[MAX_PATH] = L"";

    hr = WcaInitialize(hInstall, "BrowseForFile");
    ExitOnFailure(hr, "Failed to initialize");

    WcaLog(LOGMSG_STANDARD, "Initialized.");

    // Get the current file path from the property
    hr = WcaGetProperty(L"SELECTEDFILEPATH", &szFilePath);
    ExitOnFailure(hr, "The expected property SELECTEDFILEPATH was not found.");

    // Prepare variables
    SecureZeroMemory(szFoundFileName, sizeof(szFoundFileName));
    SecureZeroMemory(&ofn, sizeof(ofn));

    // If there's already a file path, use it as the starting point
    if (szFilePath && *szFilePath)
    {
        StringCchCopy(szFoundFileName, sizeof(szFoundFileName) / sizeof(szFoundFileName[0]), szFilePath);
    }

    // Prepare the OPENFILENAME structure
    ofn.lStructSize = sizeof(ofn);
    ofn.lpstrTitle = L"Select JSON Configuration File";
    ofn.hwndOwner = GetActiveWindow(); // Can also use NULL to be a little bit safer, although the dialog won't be modal in that case
    ofn.lpstrFile = szFoundFileName;
    ofn.nMaxFile = sizeof(szFoundFileName) / sizeof(szFoundFileName[0]);
    ofn.lpstrInitialDir = NULL;
    ofn.lpstrFilter = L"JSON Files\0*.json\0All Files\0*.*\0\0";
    ofn.nFilterIndex = 1;
    ofn.lpstrDefExt = L"json";
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST | OFN_HIDEREADONLY;

    // Present dialog to user to select a file
    if (GetOpenFileName(&ofn))
    {
        // User selected a file; populate the property
        WcaSetProperty(L"SELECTEDFILEPATH", ofn.lpstrFile);
    }

LExit:
    ReleaseStr(szFilePath);
    er = SUCCEEDED(hr) ? ERROR_SUCCESS : ERROR_INSTALL_FAILURE;
    return WcaFinalize(er);
}

// DllMain - Initialize and cleanup WiX custom action utils.
extern "C" BOOL WINAPI DllMain(
    __in HINSTANCE hInst,
    __in ULONG ulReason,
    __in LPVOID
    )
{
    switch(ulReason)
    {
    case DLL_PROCESS_ATTACH:
        WcaGlobalInitialize(hInst);
        break;

    case DLL_PROCESS_DETACH:
        WcaGlobalFinalize();
        break;
    }

    return TRUE;
}
