@echo off
echo ESXP Edge Agent MSI Uninstaller
echo ==============================
echo.

set PRODUCT_NAME=ESXPEdgeAgent
set OUTPUT_DIR=output
set MSI_PATH=%OUTPUT_DIR%\%PRODUCT_NAME%.msi
set LOG_PATH=%OUTPUT_DIR%\uninstall_log.txt

echo Checking for MSI file...
if not exist "%MSI_PATH%" (
    echo Error: MSI file not found at %MSI_PATH%
    echo.
    echo Attempting alternative uninstallation methods...
    goto :ALTERNATIVE_UNINSTALL
)

echo MSI file found. Attempting standard uninstallation...
echo.
echo Running uninstaller with verbose logging...
msiexec /x "%MSI_PATH%" /l*v "%LOG_PATH%" /qb

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Standard uninstallation failed. Trying alternative methods...
    goto :ALTERNATIVE_UNINSTALL
) else (
    echo.
    echo Uninstallation completed successfully.
    echo Log file saved to %LOG_PATH%
    goto :END
)

:ALTERNATIVE_UNINSTALL
echo.
echo Attempting to find product code in registry...
echo.

:: Use PowerShell to find the product code
powershell -Command "Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like '*ESXP Edge Agent*' } | ForEach-Object { Write-Host 'Found product:' $_.Name 'with ID:' $_.IdentifyingNumber; $_.Uninstall() }"

echo.
echo If uninstallation failed, you can try the following manual steps:
echo 1. Open Control Panel
echo 2. Go to Programs and Features
echo 3. Find and uninstall 'ESXP Edge Agent'
echo.
echo Alternatively, run the following command in an elevated PowerShell prompt:
echo Get-WmiObject -Class Win32_Product ^| Where-Object { $_.Name -like '*ESXP Edge Agent*' } ^| ForEach-Object { $_.Uninstall() }

:END
echo.
echo Press any key to exit...
pause > nul
