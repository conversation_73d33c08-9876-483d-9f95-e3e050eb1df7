**********************
PowerShell transcript start
Start time: 20250525212816
Username: APA\SESA808825
RunAs User: APA\SESA808825
Configuration Name: 
Machine: WIN01502063L (Microsoft Windows NT 10.0.22631.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -noexit -command try { . "c:\Users\<USER>\AppData\Local\Programs\Windsurf\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch {}
Process ID: 16788
PSVersion: 7.5.0
PSEdition: Core
GitCommitId: 7.5.0
OS: Microsoft Windows 10.0.22631
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_212816.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_212816.log
[2025-05-25 21:28:16] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 21:28:16] [INFO] Checking for Podman installation...
Podman is already installed.
Using provided config file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\tools\examples\unified-env.json
[2025-05-25 21:28:16] [INFO] Looking for docker images in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars
[2025-05-25 21:28:16] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar (Size: 159188992 bytes)
[2025-05-25 21:28:16] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\octopus-service.tar into Podman...
[2025-05-25 21:28:19] [INFO] Podman load output: Loaded image: docker.io/library/octopus-service:latest
[2025-05-25 21:28:19] [INFO] Found image file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar (Size: 52736000 bytes)
[2025-05-25 21:28:19] [INFO] Loading image C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\docker_tars\ewp.tar into Podman...
[2025-05-25 21:28:20] [INFO] Podman load output: Loaded image: docker.io/library/ewp-edge:latest
[2025-05-25 21:28:20] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 21:28:20] [INFO] Adding environment variable: REST_PORT=9080
[2025-05-25 21:28:20] [INFO] Adding environment variable: REST_HOST=0.0.0.0
[2025-05-25 21:28:20] [INFO] Adding environment variable: CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem
[2025-05-25 21:28:20] [INFO] Adding environment variable: CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem
[2025-05-25 21:28:20] [INFO] Adding environment variable: CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem
[2025-05-25 21:28:20] [INFO] Adding environment variable: CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem
[2025-05-25 21:28:20] [INFO] Adding environment variable: CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt
[2025-05-25 21:28:20] [INFO] Adding environment variable: CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com
[2025-05-25 21:28:20] [INFO] Adding environment variable: DB_PATH=/app/db
[2025-05-25 21:28:20] [INFO] Adding environment variable: AUTH_DB_PATH=/app/db/auth/auth.db
[2025-05-25 21:28:20] [INFO] Adding environment variable: AUTH_CONFIG_DB_PATH=/app/db/auth/config.db
[2025-05-25 21:28:20] [INFO] Adding environment variable: SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db
[2025-05-25 21:28:20] [INFO] Adding environment variable: CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db
[2025-05-25 21:28:20] [INFO] Processing environment variables from PSCustomObject
[2025-05-25 21:28:20] [INFO] Adding environment variable: API_URL=http://locolhost:9080
[2025-05-25 21:28:20] [INFO] Adding environment variable: NGINX_PORT=80
[2025-05-25 21:28:20] [INFO] Using volume mappings from config file
[2025-05-25 21:28:20] [INFO] Processing volume mappings from hashtable format
[2025-05-25 21:28:20] [INFO] Adding volume mapping: C:\data1\backend\logs -> /app/logs
[2025-05-25 21:28:20] [INFO] Adding volume mapping: C:\data1\backend\certs -> /app/certs
[2025-05-25 21:28:20] [INFO] Adding volume mapping: C:\data1\backend\db -> /app/db
[2025-05-25 21:28:20] [INFO] Checking and creating host directories for volume mappings...
[2025-05-25 21:28:20] [INFO] Directory already exists: C:\data1\backend\logs
[2025-05-25 21:28:20] [INFO] Directory already exists: C:\data1\backend\certs
[2025-05-25 21:28:20] [INFO] Directory already exists: C:\data1\backend\db
[2025-05-25 21:28:20] [INFO] Checking if eco-edge-network exists...
[2025-05-25 21:28:21] [INFO] eco-edge-network already exists
[2025-05-25 21:28:21] [INFO] Available networks:\nNETWORK ID    NAME              DRIVER b6856a0b93cb  eco-edge-network  bridge 2f259bab93aa  podman            bridge
[2025-05-25 21:28:21] [INFO] Checking for existing containers...
[2025-05-25 21:28:21] [INFO] Found existing container: octopus-service
[2025-05-25 21:28:21] [INFO] Stopping container: octopus-service
[2025-05-25 21:28:28] [INFO] Stop container output: octopus-service
[2025-05-25 21:28:28] [INFO] Removing container: octopus-service
[2025-05-25 21:28:28] [INFO] Remove container output: octopus-service
[2025-05-25 21:28:28] [INFO] Found existing container: ewp-edge
[2025-05-25 21:28:28] [INFO] Stopping container: ewp-edge
[2025-05-25 21:28:30] [INFO] Stop container output: ewp-edge
[2025-05-25 21:28:30] [INFO] Removing container: ewp-edge
[2025-05-25 21:28:31] [INFO] Remove container output: ewp-edge
[2025-05-25 21:28:31] [INFO] Running octopus-service container...
[2025-05-25 21:28:31] [INFO] Added backend environment arguments: -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e REST_PORT=9080 -e AUTH_DB_PATH=/app/db/auth/auth.db -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e REST_HOST=0.0.0.0
[2025-05-25 21:28:31] [INFO] Added volume arguments: -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\certs:/app/certs -v C:\data1\backend\db:/app/db
[2025-05-25 21:28:31] [INFO] Executing command: podman run -d --name octopus-service --restart always --network eco-edge-network --network-alias octopus-service --hostname octopus-service -p 0.0.0.0:9080:9080 -e CLOUD_CONNECTION_PRIVATE_KEY_PATH=/app/certs/privateKey.pem -e CLOUD_DEVICE_PRIVATE_KEY_PATH=/app/certs/device_key.pem -e AUTH_CONFIG_DB_PATH=/app/db/auth/config.db -e CLOUD_BOOTSTRAP_SERVER_URL=https://connectivity.preview.struxurewarecloud.com -e CLOUD_CONFIG_DB_PATH=/app/db/cloud/config.db -e CLOUD_CONNECTION_CERT_PATH=/app/certs/connectionCertificate.pem -e SNMP_CONFIG_DB_PATH=/app/db/snmp/config.db -e CLOUD_DEVICE_CERTIFICATE_PATH=/app/certs/device_cert.pem -e REST_PORT=9080 -e AUTH_DB_PATH=/app/db/auth/auth.db -e DB_PATH=/app/db -e CLOUD_CONNECTION_STRING_PATH=/app/certs/connectionString.txt -e REST_HOST=0.0.0.0 -v C:\data1\backend\logs:/app/logs -v C:\data1\backend\certs:/app/certs -v C:\data1\backend\db:/app/db --user 1000:1000 octopus-service:latest
[2025-05-25 21:28:33] [INFO] Container start output: dbd76ecebf4ce45a698772018ea20c1e96107bfa6f637a2e584e4a272cf701ad
[2025-05-25 21:28:35] [INFO] octopus-service container started successfully
[2025-05-25 21:28:35] [INFO] Backend service running on port 9080
[2025-05-25 21:28:35] [INFO] Running ewp-edge container...
[2025-05-25 21:28:35] [INFO] Added frontend environment arguments: -e NGINX_PORT=80 -e API_URL=http://locolhost:9080
[2025-05-25 21:28:35] [INFO] Executing command: podman run -d --name ewp-edge --restart always --network eco-edge-network --network-alias ewp-edge --hostname ewp-edge -p 0.0.0.0:8080:80 -e NGINX_PORT=80 -e API_URL=http://locolhost:9080 ewp-edge:latest
[2025-05-25 21:28:37] [INFO] Container start output: f5f0ae25619908e397efe0f6940da29453a5b73fc084904a14d875afaaad8a3b
[2025-05-25 21:28:39] [INFO] ewp-edge container started successfully
[2025-05-25 21:28:39] [INFO] Frontend service running on port 8080
[2025-05-25 21:28:39] [INFO] Installing Eco Edge Monitor system tray application...
[2025-05-25 21:28:39] [INFO] Using installation directory: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent
[2025-05-25 21:28:39] [INFO] Looking for eco-edge monitor files in: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge
[2025-05-25 21:28:39] [INFO] Install root directory contents: CustomAction data docker_tars eco-edge output scripts banner.bmp BUILD_INSTRUCTIONS.md build_log.txt build.bat build.ps1 dialog.bmp FileBrowse.bak.dll FileBrowseCA.cpp FileBrowseCA.def FileBrowseDlg.wxs FileBrowseStub.bak.cs InstallDirDlgCustom.wxs license.rtf msi-uninstall.bat Product.wxs README.md run-installer.bat stdafx.h uninstall-msi.bat uninstall.bat UninstallDlg.wxs WixUI_Custom.wxs
[2025-05-25 21:28:39] [INFO] Found 30 files in source directory
[2025-05-25 21:28:39] [INFO] Copying eco-edge monitor files from C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\eco-edge to C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 21:28:39] [INFO] Checking for locked files in source directory...
[2025-05-25 21:28:39] [INFO] Using robocopy for more reliable file copying
[2025-05-25 21:28:42] [INFO] Files copied successfully
[2025-05-25 21:28:42] [INFO] Setting up startup shortcut...
[2025-05-25 21:28:42] [INFO] Startup path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup
[2025-05-25 21:28:42] [INFO] Monitor executable path: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor\eco_edge_monitor.exe
[2025-05-25 21:28:42] [INFO] Shortcut file path: C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\Eco Edge Monitor.lnk
[2025-05-25 21:28:42] [INFO] Monitor executable found, creating shortcut
[2025-05-25 21:28:42] [INFO] Startup shortcut created successfully
[2025-05-25 21:28:42] [INFO] Found existing Eco Edge Monitor processes, stopping them
[2025-05-25 21:28:42] [INFO] Stopping process with ID 27128
True
[2025-05-25 21:28:42] [INFO] Process with ID 27128 stopped
[2025-05-25 21:28:42] [INFO] Stopped existing Eco Edge Monitor instances.
[2025-05-25 21:28:42] [INFO] Waiting for resources to be released...
[2025-05-25 21:28:45] [INFO] Checking for locked files in C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\eco-edge\monitor
[2025-05-25 21:28:45] [INFO] No locked files detected
[2025-05-25 21:28:45] [INFO] Starting Eco Edge Monitor application
[2025-05-25 21:28:45] [INFO] Started Eco Edge Monitor with process ID: 29444
[2025-05-25 21:28:45] [INFO] Eco Edge Monitor system tray application has been installed and started.
[2025-05-25 21:28:47] [INFO] Verified Eco Edge Monitor is running with process ID: 29444
[2025-05-25 21:28:47] [INFO] Eco Edge Agent installation complete.
**********************
PowerShell transcript end
End time: 20250525212847
**********************
