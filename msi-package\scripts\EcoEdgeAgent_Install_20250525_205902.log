**********************
PowerShell transcript start
Start time: 20250525205902
Username: APA\SESA808825
RunAs User: APA\SESA808825
Configuration Name: 
Machine: WIN01502063L (Microsoft Windows NT 10.0.22631.0)
Host Application: C:\Program Files\PowerShell\7\pwsh.dll -noexit -command try { . "c:\Users\<USER>\AppData\Local\Programs\Windsurf\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch {}
Process ID: 5540
PSVersion: 7.5.0
PSEdition: Core
GitCommitId: 7.5.0
OS: Microsoft Windows 10.0.22631
Platform: Win32NT
PSCompatibleVersions: 1.0, 2.0, 3.0, 4.0, 5.0, 5.1, 6.0, 7.0
PSRemotingProtocolVersion: 2.3
SerializationVersion: *******
WSManStackVersion: 3.0
**********************
Transcript started, output file is C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_205902.log
Logging enabled. Log file: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\EcoEdgeAgent_Install_20250525_205902.log
[2025-05-25 20:59:02] [INFO] Starting Eco Edge Agent installation...
[2025-05-25 20:59:02] [INFO] Checking for Podman installation...
[2025-05-25 20:59:02] [ERROR] Backend port 9080 is in use by process:  (PID: ). Installation cannot proceed.
PS>TerminatingError(): "Backend port 9080 is in use by process:  (PID: ). Installation cannot proceed."
>> TerminatingError(): "Backend port 9080 is in use by process:  (PID: ). Installation cannot proceed."
Backend port 9080 is in use by process:  (PID: ). Installation cannot proceed.
Exception: C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package\scripts\install.ps1:128:5
Line |
 128 |      throw $errorMsg
     |      ~~~~~~~~~~~~~~~
     | Backend port 9080 is in use by process:  (PID: ). Installation cannot proceed.

]633;D;1]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>cd 'c:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package'
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>Get-Content .\scripts\install.ps1.bak | Select-String -Pattern "monitor" -Context 5,5
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>cd 'c:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package'
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
PS>Get-Content .\scripts\install.ps1.bak | Select-String -Pattern "executable" -Context 5,5
]633;D;0]633;A]633;P;Cwd=C:\x5cvinayak_pc\x5cESXP-Edge-Agent\x5ceco-agent\x5cmsi-packagePS C:\vinayak_pc\ESXP-Edge-Agent\eco-agent\msi-package> ]633;B
