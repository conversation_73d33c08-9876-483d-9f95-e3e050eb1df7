version: '3'

services:
  octopus-service:
    image: octopus-service:latest
    container_name: octopus-service
    ports:
      - "9080:9080"
    networks:
      - eco-edge-network
    restart: always

  ewp-edge:
    image: ewp-edge:latest
    container_name: ewp-edge
    ports:
      - "8080:80"
    depends_on:
      - octopus-service
    networks:
      - eco-edge-network
    restart: always

networks:
  eco-edge-network:
    driver: bridge
