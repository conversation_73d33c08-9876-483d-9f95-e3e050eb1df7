param(
    [string]$Version = "*******"
)

# PowerShell script to build Eco Edge Agent MSI using WiX Toolset

# Stop on error
$ErrorActionPreference = "Stop"

# Paths (adjust if your structure changes)
$wixDir = Join-Path $PSScriptRoot "WiX"
$wxsFile = Join-Path $wixDir "Product.wxs"
$wxsFileV6 = Join-Path $wixDir "EcoEdgeAgent.wxs"
$wixObj = Join-Path $wixDir "Product.wixobj"
$outputMsi = Join-Path $PSScriptRoot "EcoEdgeAgent-$Version.msi"

# Ensure WiX tools are available - look in common installation locations
function Find-WiXTools {
    # For WiX v6.0, we need to check for wix.exe instead of candle.exe/light.exe
    $global:IsWix6 = $false
    $global:WixCmd = $null
    
    # Check if WiX v6.0 CLI is in PATH
    if (Get-Command wix.exe -ErrorAction SilentlyContinue) {
        $global:IsWix6 = $true
        $global:WixCmd = "wix.exe"
        Write-Host "Found WiX v6.0 CLI in PATH"
        return $true
    }
    
    # Check if traditional WiX tools are in PATH
    if (Get-Command candle.exe -ErrorAction SilentlyContinue) {
        Write-Host "Found traditional WiX tools in PATH"
        return $true
    }

    # WiX v6.0 common paths
    $wix6Paths = @(
        "C:\Program Files\WiX Toolset\6.0",
        "C:\Program Files (x86)\WiX Toolset\6.0",
        "C:\Program Files\dotnet\tools"
    )
    
    foreach ($path in $wix6Paths) {
        if (Test-Path (Join-Path $path "wix.exe")) {
            Write-Host "Found WiX v6.0 CLI in: $path"
            # Add to PATH for this session
            $env:PATH = "$path;$env:PATH"
            $global:IsWix6 = $true
            $global:WixCmd = "wix.exe"
            return $true
        }
    }
    
    # Traditional WiX Toolset paths (v3.x, v4.x)
    $traditionalWixPaths = @(
        # WiX v3.x paths
        "C:\Program Files (x86)\WiX Toolset v3.11\bin",
        "C:\Program Files (x86)\WiX Toolset v3.10\bin",
        # WiX v4.x/v5.x paths
        "C:\Program Files\WiX Toolset v4.0\tools",
        "C:\Program Files\WiX Toolset v5.0\tools"
    )

    foreach ($path in $traditionalWixPaths) {
        if (Test-Path (Join-Path $path "candle.exe")) {
            Write-Host "Found traditional WiX tools in: $path"
            # Add to PATH for this session
            $env:PATH = "$path;$env:PATH"
            return $true
        }
    }

    return $false
}

# WiX v6.0 flag is set in Find-WiXTools function

if (-not (Find-WiXTools)) {
    Write-Error "WiX Toolset not found. You installed WiX CLI v6.0, which should be in one of these locations:"
    Write-Error "- C:\Program Files\WiX Toolset\6.0"
    Write-Error "- C:\Program Files (x86)\WiX Toolset\6.0"
    Write-Error "- C:\Program Files\dotnet\tools"
    Write-Error "Please ensure WiX is installed and the directory containing wix.exe is in your PATH."
    exit 1
}

# Update version in the appropriate WiX file
Write-Host "Setting MSI version to $Version ..."
if ($global:IsWix6) {
    # Update version in the v6 format file
    $wxsContent = Get-Content $wxsFileV6
    # Only replace Version attribute in the Package tag, not in the XML declaration
    $wxsContent = $wxsContent -replace '<Package([^>]*)Version="[^"]+"', ('<Package$1Version="' + $Version + '"')
    Set-Content $wxsFileV6 $wxsContent
}
else {
    # Update version in the traditional format file
    $productWxsContent = Get-Content $wxsFile
    # Only replace Version attribute in the Product tag, not in the XML declaration
    $productWxsContent = $productWxsContent -replace '<Product([^>]*)Version="[^"]+"', ('<Product$1Version="' + $Version + '"')
    Set-Content $wxsFile $productWxsContent
}

# Build the MSI using the appropriate WiX tools
Push-Location $wixDir

if ($global:IsWix6) {
    # WiX v6.0 CLI uses a single command with the v6 format file
    Write-Host "Building MSI with WiX v6.0 CLI..."
    & wix.exe build -o $outputMsi EcoEdgeAgent.wxs -ext WixToolset.UI.wixext -ext WixToolset.Util.wixext -ext WixToolset.Netfx.wixext -ext WixToolset.Firewall.wixext -ext WixToolset.Iis.wixext -ext WixToolset.Msmq.wixext -ext WixToolset.Sql.wixext -ext WixToolset.ComPlus.wixext -ext WixToolset.Dependency.wixext -ext WixToolset.DirectX.wixext -ext WixToolset.Http.wixext -ext WixToolset.VisualStudio.wixext
    if ($LASTEXITCODE -ne 0) { 
        Pop-Location
        throw "wix.exe build failed" 
    }
}
else {
    # Traditional WiX Toolset (v3.x, v4.x, v5.x)
    # Compile .wxs to .wixobj
    Write-Host "Compiling $wxsFile with traditional WiX tools..."
    & candle.exe Product.wxs
    if ($LASTEXITCODE -ne 0) { 
        Pop-Location
        throw "candle.exe failed" 
    }

    # Link .wixobj to .msi
    Write-Host "Linking to $outputMsi..."
    & light.exe -ext WixUIExtension Product.wixobj -o $outputMsi
    if ($LASTEXITCODE -ne 0) { 
        Pop-Location
        throw "light.exe failed" 
    }
}

Pop-Location

Write-Host "MSI build complete: $outputMsi"
