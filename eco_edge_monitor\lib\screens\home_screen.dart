import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/service_status.dart';
import '../providers/eco_edge_provider.dart';
import '../widgets/service_card.dart';
import '../widgets/settings_dialog.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Eco Edge Monitor'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: () {
              Provider.of<EcoEdgeProvider>(context, listen: false).refreshStatus();
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: 'Settings',
            onPressed: () => _showSettingsDialog(context),
          ),
        ],
      ),
      body: Consumer<EcoEdgeProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading && provider.status == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (provider.error != null && provider.status == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 60,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${provider.error}',
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      provider.refreshStatus();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final status = provider.status;
          if (status == null) {
            return const Center(
              child: Text('No data available'),
            );
          }

          return RefreshIndicator(
            onRefresh: () => provider.refreshStatus(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatusHeader(context, status),
                  const SizedBox(height: 16),
                  ServiceCard(
                    service: status.frontendService,
                    onStart: () => provider.startService('Frontend'),
                    onStop: () => provider.stopService('Frontend'),
                    onRestart: () => provider.restartService('Frontend'),
                  ),
                  ServiceCard(
                    service: status.backendService,
                    onStart: () => provider.startService('Backend'),
                    onStop: () => provider.stopService('Backend'),
                    onRestart: () => provider.restartService('Backend'),
                  ),
                  const SizedBox(height: 16),
                  _buildActionButtons(context, provider),
                  const SizedBox(height: 24),
                  _buildLastUpdatedInfo(status.lastUpdated),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusHeader(BuildContext context, EcoEdgeStatus status) {
    Color color;
    IconData icon;
    String statusText;

    switch (status.overallState) {
      case ServiceState.running:
        color = Colors.green;
        icon = Icons.check_circle;
        statusText = 'All services are running';
        break;
      case ServiceState.stopped:
        color = Colors.red;
        icon = Icons.cancel;
        statusText = 'Some services are stopped';
        break;
      case ServiceState.warning:
        color = Colors.orange;
        icon = Icons.warning;
        statusText = 'Some services have warnings';
        break;
      case ServiceState.unknown:
      default:
        color = Colors.grey;
        icon = Icons.help;
        statusText = 'Service status unknown';
        break;
    }

    return Card(
      color: color.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 36,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statusText,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Frontend: ${_getStatusText(status.frontendService.state)} | '
                    'Backend: ${_getStatusText(status.backendService.state)}',
                    style: TextStyle(
                      color: color.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, EcoEdgeProvider provider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton.icon(
          icon: const Icon(Icons.play_arrow),
          label: const Text('Start All'),
          onPressed: () => provider.startAllServices(),
        ),
        ElevatedButton.icon(
          icon: const Icon(Icons.stop),
          label: const Text('Stop All'),
          onPressed: () => provider.stopAllServices(),
        ),
        ElevatedButton.icon(
          icon: const Icon(Icons.refresh),
          label: const Text('Restart All'),
          onPressed: () => provider.restartAllServices(),
        ),
      ],
    );
  }

  Widget _buildLastUpdatedInfo(DateTime lastUpdated) {
    return Center(
      child: Text(
        'Last updated: ${_formatDateTime(lastUpdated)}',
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 12,
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}:'
        '${dateTime.second.toString().padLeft(2, '0')}';
  }

  String _getStatusText(ServiceState state) {
    switch (state) {
      case ServiceState.running:
        return 'Running';
      case ServiceState.stopped:
        return 'Stopped';
      case ServiceState.warning:
        return 'Warning';
      case ServiceState.unknown:
      default:
        return 'Unknown';
    }
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const SettingsDialog(),
    );
  }
}
