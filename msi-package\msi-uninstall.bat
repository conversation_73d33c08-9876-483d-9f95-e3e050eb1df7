@echo off
:: This batch file is included in the MSI package
:: It runs the uninstall.ps1 script with elevated privileges

echo Running Eco Edge Agent uninstallation script...
echo This will follow the 6-step uninstallation process:
echo 1. Remove the startup link
echo 2. Kill and remove the Flutter application
echo 3. Stop and remove all containers
echo 4. Remove all images
echo 5. Remove all data and configuration files
echo 6. Remove all registry entries and startup configurations
echo.

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Administrator privileges required.
    echo Attempting to elevate...
    powershell -Command "Start-Process '%~dpnx0' -Verb RunAs"
    exit /b
)

:: Set the log file path
set LOG_FILE=%TEMP%\eco_edge_uninstall_log.txt
echo Log file will be created at: %LOG_FILE%
echo.

:: Get the installation directory
set INSTALL_DIR=%~dp0

:: Run the uninstall script with elevated privileges
echo Running uninstall.ps1 script...
powershell.exe -NoProfile -ExecutionPolicy Bypass -Command "& { Start-Process PowerShell -ArgumentList '-NoProfile -ExecutionPolicy Bypass -File \"%INSTALL_DIR%scripts\uninstall.ps1\"' -Verb RunAs -Wait }"

echo.
echo Uninstallation completed.

:: Open the log file if it exists
if exist "%LOG_FILE%" (
    echo.
    echo Log file created at: %LOG_FILE%
    echo Opening log file...
    start notepad "%LOG_FILE%"
)

echo.
echo Press any key to exit...
pause >nul
