<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
   <!--
      Product.wxs - Main WiX file for creating an installer with:
      1. Installation directory selection UI
      2. File selection UI with browse button
   -->

   <?define SourceDir="."?>

   <!-- Define product information -->
   <?define ProductName="ESXP Edge Agent" ?>
   <?define Manufacturer="Schneider Electric" ?>
   <?define ProductVersion="1.0.0" ?>
   <!-- UpgradeCode should be unique for each product Dont change it -->
   <?define UpgradeCode="1C1735A4-80E5-4969-8421-3A437C7010BC" ?> 

   <Product Id="*"
            Name="$(var.ProductName)"
            Language="1033"
            Version="$(var.ProductVersion)"
            Manufacturer="$(var.Manufacturer)"
            UpgradeCode="$(var.UpgradeCode)">

      <!-- Package information -->
      <Package InstallerVersion="200"
               Compressed="yes"
               InstallScope="perUser"
               Description="Installer for $(var.ProductName)" />

      <!-- Media -->
      <Media Id="1" Cabinet="product.cab" EmbedCab="yes" />

      <!-- Upgrade information -->
      <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed." />

      <!-- UI customization -->
      <!-- Commented out for now since we're having issues with the bitmap files
      <WixVariable Id="WixUIBannerBmp" Value="banner.bmp" />
      <WixVariable Id="WixUIDialogBmp" Value="dialog.bmp" />
      -->
      <WixVariable Id="WixUILicenseRtf" Value="license.rtf" />

      <!-- Define properties for UI -->
      <!-- Set WIXUI_INSTALLDIR property to show the installation path -->
      <Property Id="WIXUI_INSTALLDIR" Value="INSTALLDIR" />
      <Property Id="WIXUI_DONTVALIDATEPATH" Value="1" />
      <!-- Property for the selected configuration file path -->
      <Property Id="SELECTEDFILEPATH" Secure="yes" />

      <!-- No uninstall options needed - we'll always remove everything -->

      <!-- Directory structure -->
      <Directory Id="TARGETDIR" Name="SourceDir">
         <Directory Id="LocalAppDataFolder">
            <Directory Id="INSTALLDIR" Name="EcoEdgeAgent">
               <!-- Define components here -->
               <Component Id="ProductComponent" Guid="*">
                  <File Id="ProductExecutable" Name="Product.wxs" Source="Product.wxs" KeyPath="yes" />
               </Component>

               <!-- Uninstall batch file -->
               <Component Id="UninstallBatchComponent" Guid="{7AD0B8AB-9CBA-43EA-8DA5-781458DACD54}">
                  <File Id="UninstallBatch" Name="uninstall.bat" Source="uninstall.bat" KeyPath="yes" />
               </Component>

               <!-- Set permissions on the installation directory -->
               <Component Id="PermissionsComponent" Guid="{B4CEBAB9-710F-4E85-BC58-7078AA3420F9}" KeyPath="yes">
                  <CreateFolder>
                     <!-- Grant Users group full control of the installation directory -->
                     <Permission User="Users" GenericAll="yes" />
                  </CreateFolder>
               </Component>

               <!-- Docker Tars Folder -->
               <Directory Id="DockerTarsDir" Name="docker_tars">
                  <Component Id="DockerTarsComponent_EwpTar" Guid="{466350F1-08D6-4E4C-B8D3-C277BA61C30E}">
                     <File Id="EwpTar" Name="ewp.tar" Source="docker_tars\ewp.tar" KeyPath="yes" />
                  </Component>
                  <Component Id="DockerTarsComponent_OctopusServiceTar" Guid="{3C28847D-AF05-4DF2-AF7F-962FE41B4FE9}">
                     <File Id="OctopusServiceTar" Name="octopus-service.tar" Source="docker_tars\octopus-service.tar" KeyPath="yes" />
                  </Component>
               </Directory>

               <!-- Flutter Assets Directory Structure -->
               <Directory Id="EcoEdgeSourceDir" Name="eco-edge">
                   <Directory Id="DataSourceDir" Name="data">
                       <Directory Id="FlutterAssetsSourceDir" Name="flutter_assets">
                           <!-- Flutter assets will be added here by the generated FlutterAssets.wxs -->
                       </Directory>
                   </Directory>
               </Directory>

               <!-- Scripts Folder -->
               <Directory Id="ScriptsDir" Name="scripts">
                  <Component Id="ScriptsComponent_InstallScript" Guid="{CF7C3CA8-5FA0-419B-AA94-3DF86522F896}">
                     <File Id="InstallScript" Name="install.ps1" Source="scripts\install.ps1" KeyPath="yes" />
                  </Component>
                  <Component Id="ScriptsComponent_InstallTestScript" Guid="{E13B5021-59BB-4B05-8582-D3BDBECCC321}">
                     <File Id="InstallTestScript" Name="install_test.ps1" Source="scripts\install_test.ps1" KeyPath="yes" />
                  </Component>
                  <Component Id="ScriptsComponent_InstallSimpleScript" Guid="{359F1D47-85B3-40DD-B080-B7E35AD0AC35}">
                     <File Id="InstallSimpleScript" Name="install_simple.ps1" Source="scripts\install_simple.ps1" KeyPath="yes" />
                  </Component>
                  <Component Id="ScriptsComponent_UninstallScript" Guid="{CC265BCB-D5E3-4339-9D90-C131F9FC0F51}">
                     <File Id="UninstallScript" Name="uninstall.ps1" Source="scripts\uninstall.ps1" KeyPath="yes" />
                  </Component>
                  <Component Id="ScriptsComponent_MsiUninstallBat" Guid="{3D27ACF2-BA1A-4C30-BC47-B98A2E1B190F}">
                     <File Id="MsiUninstallBat" Name="msi-uninstall.bat" Source="msi-uninstall.bat" KeyPath="yes" />
                  </Component>
               </Directory>

               <!-- Eco Edge Folder -->
               <Directory Id="EcoEdgeInstallDir" Name="eco-edge">
                  <Component Id="EcoEdgeComponent_EcoEdgeExe" Guid="{F972D98F-9A0A-410E-AE43-A10A709FD91B}">
                     <File Id="EcoEdgeExe" Name="eco_edge_monitor.exe" Source="eco-edge\eco_edge_monitor.exe" KeyPath="yes" />
                  </Component>
                  <Component Id="EcoEdgeComponent_FlutterDll" Guid="{372B65F2-8731-4E2D-95B4-C59071EEA7C9}">
                     <File Id="FlutterDll" Name="flutter_windows.dll" Source="eco-edge\flutter_windows.dll" KeyPath="yes" />
                  </Component>
                  <Component Id="EcoEdgeComponent_ScreenRetrieverPlugin" Guid="{9F3E3E37-8700-470F-9C50-F4F44671880B}">
                     <File Id="ScreenRetrieverPlugin" Name="screen_retriever_plugin.dll" Source="eco-edge\screen_retriever_plugin.dll" KeyPath="yes" />
                  </Component>
                  <Component Id="EcoEdgeComponent_SystemTrayPlugin" Guid="{DAF302A9-A221-4139-ACE4-A74C955D4204}">
                     <File Id="SystemTrayPlugin" Name="system_tray_plugin.dll" Source="eco-edge\system_tray_plugin.dll" KeyPath="yes" />
                  </Component>
                  <Component Id="EcoEdgeComponent_TrayManagerPlugin" Guid="{83958834-E1FA-402A-A4AB-8C197F760E7C}">
                     <File Id="TrayManagerPlugin" Name="tray_manager_plugin.dll" Source="eco-edge\tray_manager_plugin.dll" KeyPath="yes" />
                  </Component>
                  <Component Id="EcoEdgeComponent_UrlLauncherPlugin" Guid="{4E99FBEC-8A24-4A26-B771-5C032EF05E9E}">
                     <File Id="UrlLauncherPlugin" Name="url_launcher_windows_plugin.dll" Source="eco-edge\url_launcher_windows_plugin.dll" KeyPath="yes" />
                  </Component>
                  <Component Id="EcoEdgeComponent_WindowManagerPlugin" Guid="{6B3A06BA-1F09-4551-9EC0-AD6B68CDE498}">
                     <File Id="WindowManagerPlugin" Name="window_manager_plugin.dll" Source="eco-edge\window_manager_plugin.dll" KeyPath="yes" />
                  </Component>

                  <!-- Data Subfolder -->
                  <Directory Id="EcoEdgeDataDir" Name="data">
                     <Component Id="EcoEdgeDataComponent_AppSo" Guid="{24AC0AB8-3079-47EA-B4BB-C2B7C931108E}">
                        <File Id="AppSo" Name="app.so" Source="eco-edge\data\app.so" KeyPath="yes" />
                     </Component>
                     <Component Id="EcoEdgeDataComponent_IcudtlDat" Guid="{9482B84E-02D9-41E5-8A2B-23441A97FCE2}">
                        <File Id="IcudtlDat" Name="icudtl.dat" Source="eco-edge\data\icudtl.dat" KeyPath="yes" />
                     </Component>

                     <!-- Flutter Assets Subfolder -->
                     <Directory Id="FlutterAssetsInstallDir" Name="flutter_assets">
                        <Component Id="FlutterAssetsComponent_Folder" Guid="{51842021-1C36-4B35-BC20-FF94CA4C8D99}">
                           <CreateFolder />
                        </Component>
                     </Directory>
                  </Directory>
               </Directory>
            </Directory>
         </Directory>
      </Directory>

      <!-- Features -->
      <Feature Id="ProductFeature" Title="ESXP Edge Agent" Level="1">
         <ComponentRef Id="ProductComponent" />
         <ComponentRef Id="UninstallBatchComponent" />
         <ComponentRef Id="PermissionsComponent" />
         <ComponentRef Id="DockerTarsComponent_EwpTar" />
         <ComponentRef Id="DockerTarsComponent_OctopusServiceTar" />
         <ComponentRef Id="ScriptsComponent_InstallScript" />
         <ComponentRef Id="ScriptsComponent_UninstallScript" />
         <ComponentRef Id="ScriptsComponent_InstallTestScript" />
         <ComponentRef Id="ScriptsComponent_InstallSimpleScript" />
         <!-- Always include the Flutter Assets -->
         <ComponentGroupRef Id="FlutterAssetsGroup" />
         <ComponentRef Id="ScriptsComponent_MsiUninstallBat" />

         <!-- Eco Edge Components -->
         <ComponentRef Id="EcoEdgeComponent_EcoEdgeExe" />
         <ComponentRef Id="EcoEdgeComponent_FlutterDll" />
         <ComponentRef Id="EcoEdgeComponent_ScreenRetrieverPlugin" />
         <ComponentRef Id="EcoEdgeComponent_SystemTrayPlugin" />
         <ComponentRef Id="EcoEdgeComponent_TrayManagerPlugin" />
         <ComponentRef Id="EcoEdgeComponent_UrlLauncherPlugin" />
         <ComponentRef Id="EcoEdgeComponent_WindowManagerPlugin" />

         <!-- Eco Edge Data Components -->
         <ComponentRef Id="EcoEdgeDataComponent_AppSo" />
         <ComponentRef Id="EcoEdgeDataComponent_IcudtlDat" />
         <ComponentRef Id="FlutterAssetsComponent_Folder" />

         <!-- FlutterAssetsGroup is already included above -->
      </Feature>

      <!-- Custom UI -->
      <UI Id="WixUI_ProductUI">
         <!-- Use our custom UI sequence -->
         <UIRef Id="WixUI_Custom" />
      </UI>

      <!-- Custom action for file browsing -->
      <Binary Id="FileBrowseBinary" SourceFile="$(var.SourceDir)\CustomAction\x64\FileBrowse.dll" />
      <CustomAction Id="BrowseForFile" BinaryKey="FileBrowseBinary" DllEntry="BrowseForFile" />
      
      <!-- Custom action to run the install.ps1 script -->
      <!-- Create properties for the script path -->
      <Property Id="POWERSHELLEXE">powershell.exe</Property>
      <Property Id="INSTALLSCRIPT" Secure="yes" />
      
      <!-- Set the INSTALLSCRIPT property using a custom action -->
      <CustomAction Id="SetInstallScript" Property="INSTALLSCRIPT" Value="[INSTALLDIR]scripts\install.ps1" />

      <!-- Run the install script directly -->
      <CustomAction Id="RunInstallScript" 
                   ExeCommand="&quot;[POWERSHELLEXE]&quot; -NoProfile -ExecutionPolicy Bypass -WindowStyle Hidden -File &quot;[INSTALLSCRIPT]&quot; -ConfigFilePath &quot;[SELECTEDFILEPATH]&quot;" 
                   Directory="INSTALLDIR"
                   Return="check" 
                   Execute="deferred" 
                   Impersonate="no" />

      <!-- Run the uninstall script directly -->
      <!-- Property to store uninstall script path -->
      <Property Id="UNINSTALLSCRIPT" Secure="yes" />
      
      <!-- Set the UNINSTALLSCRIPT property using a custom action -->
      <CustomAction Id="SetUninstallScript" Property="UNINSTALLSCRIPT" Value="[INSTALLDIR]scripts\uninstall.ps1" />
      
      <!-- Run the uninstall script with immediate execution to avoid path issues -->
      <CustomAction Id="RunUninstallScript" 
                   ExeCommand="&quot;[POWERSHELLEXE]&quot; -NoProfile -ExecutionPolicy Bypass -WindowStyle Hidden -File &quot;[UNINSTALLSCRIPT]&quot;" 
                   Directory="INSTALLDIR"
                   Return="check" />

      <!-- No uninstall options needed - we'll always remove everything -->

      <!-- Set the install/uninstall script custom actions to run at appropriate times -->
      <InstallExecuteSequence>
        <!-- Set the INSTALLSCRIPT property before running the install script -->
        <Custom Action="SetInstallScript" After="InstallFiles">NOT Installed AND NOT REMOVE</Custom>
        
        <!-- Install script runs after files are installed during installation -->
        <Custom Action="RunInstallScript" After="SetInstallScript">NOT Installed AND NOT REMOVE</Custom>

        <!-- No environment variables needed for uninstallation -->

        <!-- Temporarily disabled uninstall script execution
        <Custom Action="SetUninstallScript" Before="RemoveFiles">REMOVE="ALL"</Custom>
        
        <Custom Action="RunUninstallScript" After="SetUninstallScript">REMOVE="ALL"</Custom>
        -->
      </InstallExecuteSequence>

      <!-- No UI needed for uninstallation options -->
   </Product>
</Wix>
