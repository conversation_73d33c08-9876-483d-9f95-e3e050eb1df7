import 'dart:async';
import 'package:eco_edge_monitor/models/constants/utils.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/service_status.dart';
import '../services/podman_service.dart';

class EcoEdgeProvider with ChangeNotifier {
  final PodmanService _podmanService = PodmanService();
  EcoEdgeStatus? _status;
  Timer? _refreshTimer;
  bool _isLoading = false;
  String? _error;

  // Settings
  int _refreshInterval = 5; // seconds
  bool _startMinimized = true;
  bool _startOnBoot = true;
  
  // Flag to control auto-starting containers during status refresh
  bool _autoStartContainers = true;
  
  // Flag to track whether containers should be kept stopped
  // This persists across refreshes
  bool _keepContainersStopped = false;

  EcoEdgeProvider() {
    _loadSettings().then((_) {
      // First refresh status to check current state
      refreshStatus().then((_) {
        // Auto-start containers if enabled
        if (_startOnBoot) {
          startAllServices();
        }
      });
      _startRefreshTimer();
    });
  }

  EcoEdgeStatus? get status => _status;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get refreshInterval => _refreshInterval;
  bool get startMinimized => _startMinimized;
  bool get startOnBoot => _startOnBoot;

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _refreshInterval = prefs.getInt('refreshInterval') ?? 5;
      _startMinimized = prefs.getBool('startMinimized') ?? true;
      _startOnBoot = prefs.getBool('startOnBoot') ?? true;
      notifyListeners();
    } catch (e) {
      print('Error loading settings: $e');
    }
  }

  Future<void> saveSettings({
    int? refreshInterval,
    bool? startMinimized,
    bool? startOnBoot,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (refreshInterval != null) {
        _refreshInterval = refreshInterval;
        await prefs.setInt('refreshInterval', refreshInterval);
      }

      if (startMinimized != null) {
        _startMinimized = startMinimized;
        await prefs.setBool('startMinimized', startMinimized);
      }

      if (startOnBoot != null) {
        _startOnBoot = startOnBoot;
        await prefs.setBool('startOnBoot', startOnBoot);
      }

      _restartRefreshTimer();
      notifyListeners();
    } catch (e) {
      print('Error saving settings: $e');
    }
  }

  void _startRefreshTimer() {
    _refreshTimer = Timer.periodic(
      Duration(seconds: _refreshInterval),
      (_) => refreshStatus(),
    );
    refreshStatus(); // Initial refresh
  }

  void _restartRefreshTimer() {
    _refreshTimer?.cancel();
    _startRefreshTimer();
  }

  Future<void> refreshStatus() async {
    if (_isLoading) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Only auto-start containers if they're not supposed to be kept stopped
      bool shouldAutoStart = _autoStartContainers && !_keepContainersStopped;
      _status = await _podmanService.getEcoEdgeStatus(shouldAutoStart);
      _error = null;
    } catch (e) {
      _error = e.toString();
      print('Error refreshing status: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> startService(String serviceName) async {
    try {
      // Reset the flag to allow auto-starting again
      _keepContainersStopped = false;
      
      if (serviceName == 'Frontend') {
        await _podmanService.startContainer(
          EcoEdgeConstants.frontendContainerName,
        );
      } else if (serviceName == 'Backend') {
        await _podmanService.startContainer(
          EcoEdgeConstants.backendContainerName,
        );
      }
      await refreshStatus();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<void> stopService(String serviceName) async {
    try {
      // Set flag to keep containers stopped
      _keepContainersStopped = true;
      
      if (serviceName == 'Frontend') {
        await _podmanService.stopContainer(
          EcoEdgeConstants.frontendContainerName,
        );
      } else if (serviceName == 'Backend') {
        await _podmanService.stopContainer(
          EcoEdgeConstants.backendContainerName,
        );
      }
      
      // Temporarily disable auto-starting for this refresh
      _autoStartContainers = false;
      await refreshStatus();
      _autoStartContainers = true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      // Make sure to re-enable auto-starting even if there's an error
      _autoStartContainers = true;
    }
  }

  Future<void> restartService(String serviceName) async {
    try {
      if (serviceName == 'Frontend') {
        await _podmanService.restartContainer(
          EcoEdgeConstants.frontendContainerName,
        );
      } else if (serviceName == 'Backend') {
        await _podmanService.restartContainer(
          EcoEdgeConstants.backendContainerName,
        );
      }
      await refreshStatus();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<void> startAllServices() async {
    try {
      // Reset the flag to allow auto-starting again
      _keepContainersStopped = false;
      
      await _podmanService.startContainer(
        EcoEdgeConstants.backendContainerName,
      );
      await _podmanService.startContainer(
        EcoEdgeConstants.frontendContainerName,
      );
      await refreshStatus();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<void> stopAllServices() async {
    try {
      // Set flag to keep containers stopped
      _keepContainersStopped = true;
      
      await _podmanService.stopContainer(
        EcoEdgeConstants.frontendContainerName,
      );
      await _podmanService.stopContainer(EcoEdgeConstants.backendContainerName);
      
      // Temporarily disable auto-starting for this refresh
      _autoStartContainers = false;
      await refreshStatus();
      _autoStartContainers = true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      // Make sure to re-enable auto-starting even if there's an error
      _autoStartContainers = true;
    }
  }

  Future<void> restartAllServices() async {
    try {
      await _podmanService.restartContainer(
        EcoEdgeConstants.backendContainerName,
      );
      await _podmanService.restartContainer(
        EcoEdgeConstants.frontendContainerName,
      );
      await refreshStatus();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }
}
