@echo off
REM run-installer.bat - Run the MSI installer after it's built

set PRODUCT_NAME=ESXPEdgeAgent
set OUTPUT_DIR=output

if not exist "%OUTPUT_DIR%\%PRODUCT_NAME%.msi" (
    echo Error: MSI installer not found at %OUTPUT_DIR%\%PRODUCT_NAME%.msi
    echo Please build the installer first using build.ps1 or build.bat
    exit /b 1
)

echo Running %PRODUCT_NAME% installer...
msiexec /i "%OUTPUT_DIR%\%PRODUCT_NAME%.msi" /l*v "%OUTPUT_DIR%\install_log.txt"

echo.
echo Installation complete. Log file saved to %OUTPUT_DIR%\install_log.txt
