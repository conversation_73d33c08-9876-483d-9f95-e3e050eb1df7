@echo off
echo Starting Eco Edge Agent Installation Wrapper...

set CONFIG_PATH=%1
echo Config path: %CONFIG_PATH%

:: Run PowerShell with elevated privileges if needed
if "%CONFIG_PATH%"=="" (
  powershell.exe -NoProfile -ExecutionPolicy Bypass -File "%~dp0install.ps1"
) else (
  powershell.exe -NoProfile -ExecutionPolicy Bypass -File "%~dp0install.ps1" -ConfigFilePath "%CONFIG_PATH%"
)

if %ERRORLEVEL% NEQ 0 (
  echo Installation failed with error code %ERRORLEVEL%
  exit /b %ERRORLEVEL%
)

echo Installation completed successfully.
exit /b 0
