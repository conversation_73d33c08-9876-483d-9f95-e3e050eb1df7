import 'package:flutter/material.dart';
import '../models/service_status.dart';

class ServiceCard extends StatelessWidget {
  final ServiceStatus service;
  final VoidCallback onStart;
  final VoidCallback onStop;
  final VoidCallback onRestart;

  const ServiceCard({
    Key? key,
    required this.service,
    required this.onStart,
    required this.onStop,
    required this.onRestart,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    _buildStatusIcon(),
                    const SizedBox(width: 8),
                    Text(
                      service.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                _buildActionButtons(),
              ],
            ),
            const Divider(),
            _buildInfoRow('Status', _getStatusText()),
            _buildInfoRow('Port', service.port),
            if (service.containerName.isNotEmpty)
              _buildInfoRow('Container', service.containerName),
            if (service.containerImage.isNotEmpty)
              _buildInfoRow('Image', service.containerImage),
            const SizedBox(height: 8),
            _buildResourceUsage(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIcon() {
    Color color;
    IconData icon;

    switch (service.state) {
      case ServiceState.running:
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case ServiceState.stopped:
        color = Colors.red;
        icon = Icons.cancel;
        break;
      case ServiceState.warning:
        color = Colors.orange;
        icon = Icons.warning;
        break;
      case ServiceState.unknown:
      default:
        color = Colors.grey;
        icon = Icons.help;
        break;
    }

    return Icon(icon, color: color);
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        IconButton(
          icon: const Icon(Icons.play_arrow),
          tooltip: 'Start',
          onPressed: service.state == ServiceState.running ? null : onStart,
        ),
        IconButton(
          icon: const Icon(Icons.stop),
          tooltip: 'Stop',
          onPressed: service.state == ServiceState.stopped ? null : onStop,
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          tooltip: 'Restart',
          onPressed: onRestart,
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildResourceUsage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Resource Usage:',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        _buildResourceBar('CPU', service.cpuUsage, '%', Colors.blue),
        const SizedBox(height: 4),
        _buildResourceBar('Memory', service.memoryUsage, ' MB', Colors.purple),
      ],
    );
  }

  Widget _buildResourceBar(
      String label, double value, String unit, Color color) {
    return Row(
      children: [
        SizedBox(
          width: 60,
          child: Text(label),
        ),
        Expanded(
          child: LinearProgressIndicator(
            value: _getProgressValue(value),
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        SizedBox(
          width: 60,
          child: Text(
            '${value.toStringAsFixed(1)}$unit',
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  double _getProgressValue(double value) {
    if (value <= 0) return 0;
    if (value >= 100) return 1;
    return value / 100;
  }

  String _getStatusText() {
    switch (service.state) {
      case ServiceState.running:
        return 'Running';
      case ServiceState.stopped:
        return 'Stopped';
      case ServiceState.warning:
        return 'Warning';
      case ServiceState.unknown:
      default:
        return 'Unknown';
    }
  }
}
