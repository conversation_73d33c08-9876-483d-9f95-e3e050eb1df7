@echo off
REM build.bat - Batch script to build the MSI installer from WiX source files

REM Configuration variables
set PRODUCT_NAME=MyApplication
set OUTPUT_DIR=output
set WIX_BIN_PATH=C:\Program Files (x86)\WiX Toolset v3.11\bin
set VS_DEV_CMD=C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat

echo ===== Building MSI Installer for %PRODUCT_NAME% =====

REM Create output directory if it doesn't exist
if not exist %OUTPUT_DIR% (
    mkdir %OUTPUT_DIR%
    echo Created output directory: %OUTPUT_DIR%
)

REM Check if WiX tools are available
if not exist "%WIX_BIN_PATH%\candle.exe" (
    echo Error: WiX Toolset not found at %WIX_BIN_PATH%
    echo Please install WiX Toolset v3.x or update the WIX_BIN_PATH variable in this script.
    exit /b 1
)

REM Check if Visual Studio is available
if not exist "%VS_DEV_CMD%" (
    echo Error: Visual Studio Developer Command Prompt not found at %VS_DEV_CMD%
    echo Please install Visual Studio or update the VS_DEV_CMD variable in this script.
    exit /b 1
)

REM Create placeholder bitmaps if they don't exist
if not exist banner.bmp (
    echo Creating placeholder banner.bmp...
    echo ^<html^>^<body^>^<canvas id="banner" width="493" height="58"^>^</canvas^>^<script^>var c=document.getElementById("banner");var ctx=c.getContext("2d");ctx.fillStyle="#f0f0f0";ctx.fillRect(0,0,493,58);ctx.fillStyle="#000000";ctx.font="16px Arial";ctx.fillText("%PRODUCT_NAME%",10,30);^</script^>^</body^>^</html^> > banner.html
    echo Please manually save the banner.html as banner.bmp
    echo Press any key to continue...
    pause > nul
)

if not exist dialog.bmp (
    echo Creating placeholder dialog.bmp...
    echo ^<html^>^<body^>^<canvas id="dialog" width="493" height="312"^>^</canvas^>^<script^>var c=document.getElementById("dialog");var ctx=c.getContext("2d");ctx.fillStyle="#ffffff";ctx.fillRect(0,0,493,312);ctx.fillStyle="#000000";ctx.font="20px Arial";ctx.fillText("%PRODUCT_NAME% Setup",10,30);^</script^>^</body^>^</html^> > dialog.html
    echo Please manually save the dialog.html as dialog.bmp
    echo Press any key to continue...
    pause > nul
)

REM Build the custom action DLL
echo Building custom action DLL...

REM Create a temporary Visual Studio project for the custom action
set CUSTOM_ACTION_DIR=CustomAction
if not exist %CUSTOM_ACTION_DIR% mkdir %CUSTOM_ACTION_DIR%

REM Create a simple stdafx.h file if it doesn't exist
if not exist stdafx.h (
    echo #pragma once > stdafx.h
    echo #include ^<windows.h^> >> stdafx.h
)

REM Call Visual Studio Developer Command Prompt to build the DLL
call "%VS_DEV_CMD%"
echo Building FileBrowse.dll...

REM Compile the custom action DLL
cl /LD /D_USRDLL /D_UNICODE /DUNICODE /I"C:\Program Files (x86)\WiX Toolset v3.11\SDK\VS2017\inc" FileBrowseCA.cpp /link /DEF:FileBrowseCA.def /OUT:FileBrowse.dll "C:\Program Files (x86)\WiX Toolset v3.11\SDK\VS2017\lib\x86\msi.lib" "C:\Program Files (x86)\WiX Toolset v3.11\SDK\VS2017\lib\x86\dutil.lib" "C:\Program Files (x86)\WiX Toolset v3.11\SDK\VS2017\lib\x86\wcautil.lib"

if not exist FileBrowse.dll (
    echo Error: Failed to build FileBrowse.dll
    exit /b 1
)

echo Successfully built FileBrowse.dll

REM Compile WiX source files
echo Compiling WiX source files...

REM Compile the WiX source files
"%WIX_BIN_PATH%\candle.exe" -ext WixUIExtension Product.wxs WixUI_Custom.wxs FileBrowseDlg.wxs InstallDirDlgCustom.wxs -out "%OUTPUT_DIR%\"

if %ERRORLEVEL% neq 0 (
    echo Error: Failed to compile WiX source files.
    exit /b 1
)

REM Link the object files to create the MSI
echo Linking to create MSI...
"%WIX_BIN_PATH%\light.exe" -ext WixUIExtension "%OUTPUT_DIR%\Product.wixobj" "%OUTPUT_DIR%\WixUI_Custom.wixobj" "%OUTPUT_DIR%\FileBrowseDlg.wixobj" "%OUTPUT_DIR%\InstallDirDlgCustom.wixobj" -out "%OUTPUT_DIR%\%PRODUCT_NAME%.msi"

if %ERRORLEVEL% neq 0 (
    echo Error: Failed to link WiX object files.
    exit /b 1
)

REM Check if the MSI was created successfully
if exist "%OUTPUT_DIR%\%PRODUCT_NAME%.msi" (
    echo Successfully created %OUTPUT_DIR%\%PRODUCT_NAME%.msi
) else (
    echo Failed to create MSI. Check the build output for errors.
    exit /b 1
)

echo Build completed successfully!
