@echo off
echo Starting Eco Edge Agent Stage 1 Installation...

:: Create a starter script that will be called after the MSI completes
:: This approach bypasses the Windows Installer custom action security restrictions

set INSTALL_DIR=%~dp0..
set SCRIPTS_DIR=%~dp0
set CONFIG_PATH=%1

:: Create a launcher file that will be executed after MSI installation
echo @echo off > "%TEMP%\eco_edge_installer.cmd"
echo echo Running Eco Edge Agent post-installation setup... >> "%TEMP%\eco_edge_installer.cmd"
echo cd /d "%INSTALL_DIR%" >> "%TEMP%\eco_edge_installer.cmd"

if "%CONFIG_PATH%"=="" (
  echo powershell.exe -NoProfile -ExecutionPolicy Bypass -File "%SCRIPTS_DIR%install.ps1" >> "%TEMP%\eco_edge_installer.cmd"
) else (
  echo powershell.exe -NoProfile -ExecutionPolicy Bypass -File "%SCRIPTS_DIR%install.ps1" -ConfigFilePath "%CONFIG_PATH%" >> "%TEMP%\eco_edge_installer.cmd"
)

:: Launch the post-install script with a short delay to allow MSI to complete
echo start /b cmd /c "timeout /t 5 & "%TEMP%\eco_edge_installer.cmd"" >> "%TEMP%\eco_edge_installer.cmd"

:: COMMENTED OUT: Create a shortcut to run the installer in the Start Menu
:: echo Creating post-installation shortcut...
:: powershell.exe -NoProfile -ExecutionPolicy Bypass -Command "$s=(New-Object -COM WScript.Shell).CreateShortcut('%ProgramData%\Microsoft\Windows\Start Menu\Programs\EcoEdgeAgent\Complete Installation.lnk');$s.TargetPath='%TEMP%\eco_edge_installer.cmd';$s.WorkingDirectory='%INSTALL_DIR%';$s.Description='Complete Eco Edge Agent Installation';$s.IconLocation='%SystemRoot%\System32\SHELL32.dll,43';$s.Save()"

:: COMMENTED OUT: Make sure the directory exists
:: mkdir "%ProgramData%\Microsoft\Windows\Start Menu\Programs\EcoEdgeAgent" 2>nul

:: Success message
echo Stage 1 complete. Installation process has started in the background.
echo The installation will continue automatically.

exit /b 0
