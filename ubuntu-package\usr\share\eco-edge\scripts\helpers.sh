#!/bin/bash

# Helper functions for Eco Edge installation

# Determine which container engine to use (<PERSON><PERSON> or <PERSON><PERSON>)
get_container_engine() {
    if command -v docker >/dev/null 2>&1; then
        echo "docker"
    elif command -v podman >/dev/null 2>&1; then
        echo "podman"
    else
        echo "Error: Neither <PERSON><PERSON> nor <PERSON><PERSON> is installed." >&2
        exit 1
    fi
}

# Parse JSON configuration file
parse_json_config() {
    local config_file="$1"
    
    if [ ! -f "$config_file" ]; then
        echo "Error: Configuration file not found: $config_file" >&2
        return 1
    fi
    
    # Check if jq is installed
    if ! command -v jq >/dev/null 2>&1; then
        echo "Error: jq is required for JSON parsing. Please install it with 'apt-get install jq'." >&2
        exit 1
    fi
    
    # Parse the JSON file
    if ! jq empty "$config_file" 2>/dev/null; then
        echo "Error: Invalid JSON in configuration file: $config_file" >&2
        return 1
    fi
    
    # Return success
    return 0
}

# Parse .env configuration file
parse_env_config() {
    local config_file="$1"
    
    if [ ! -f "$config_file" ]; then
        echo "Error: Configuration file not found: $config_file" >&2
        return 1
    fi
    
    # Check file format (simple validation)
    if ! grep -q "=" "$config_file"; then
        echo "Error: Invalid .env file format: $config_file" >&2
        return 1
    fi
    
    # Return success
    return 0
}

# Process unified configuration file
process_config_file() {
    local config_file="$1"
    local config_type="$2"
    
    if [ ! -f "$config_file" ]; then
        echo "Error: Configuration file not found: $config_file" >&2
        return 1
    fi
    
    # Determine file type if not specified
    if [ -z "$config_type" ]; then
        if [[ "$config_file" == *.json ]]; then
            config_type="json"
        elif [[ "$config_file" == *.env ]]; then
            config_type="env"
        else
            echo "Error: Cannot determine configuration file type: $config_file" >&2
            return 1
        fi
    fi
    
    # Process based on file type
    case "$config_type" in
        json)
            parse_json_config "$config_file"
            ;;
        env)
            parse_env_config "$config_file"
            ;;
        *)
            echo "Error: Unsupported configuration file type: $config_type" >&2
            return 1
            ;;
    esac
    
    return $?
}

# Extract backend environment variables from JSON config
get_backend_env_from_json() {
    local config_file="$1"
    local env_vars=""
    
    # Extract backend environment variables
    if [ -f "$config_file" ]; then
        local vars=$(jq -r '.backendEnv | to_entries[] | "-e \(.key)=\(.value)"' "$config_file" 2>/dev/null)
        if [ $? -eq 0 ]; then
            env_vars="$vars"
        fi
    fi
    
    echo "$env_vars"
}

# Extract frontend environment variables from JSON config
get_frontend_env_from_json() {
    local config_file="$1"
    local env_vars=""
    
    # Extract frontend environment variables
    if [ -f "$config_file" ]; then
        local vars=$(jq -r '.frontendEnv | to_entries[] | "-e \(.key)=\(.value)"' "$config_file" 2>/dev/null)
        if [ $? -eq 0 ]; then
            env_vars="$vars"
        fi
    fi
    
    echo "$env_vars"
}

# Extract backend volume mounts from JSON config
get_backend_vol_from_json() {
    local config_file="$1"
    local volumes=""
    
    # Extract backend volume mounts
    if [ -f "$config_file" ]; then
        local vols=$(jq -r '.backendVol | to_entries[] | "-v \(.key):\(.value):Z"' "$config_file" 2>/dev/null)
        if [ $? -eq 0 ]; then
            volumes="$vols"
        fi
    fi
    
    echo "$volumes"
}

# Extract environment variables from .env file
get_env_from_env_file() {
    local env_file="$1"
    local prefix="$2"
    local env_vars=""
    
    # Extract environment variables with the given prefix
    if [ -f "$env_file" ]; then
        while IFS= read -r line || [ -n "$line" ]; do
            # Skip comments and empty lines
            if [[ "$line" =~ ^[[:space:]]*# ]] || [[ "$line" =~ ^[[:space:]]*$ ]]; then
                continue
            fi
            
            # Check if line has the prefix
            if [[ "$line" == ${prefix}* ]]; then
                # Remove prefix and extract key-value pair
                local kv=${line#${prefix}}
                local key=${kv%%=*}
                local value=${kv#*=}
                
                # Add to environment variables
                env_vars="$env_vars -e $key=$value"
            fi
        done < "$env_file"
    fi
    
    echo "$env_vars"
}

# Extract volume mounts from .env file
get_vol_from_env_file() {
    local env_file="$1"
    local prefix="$2"
    local volumes=""
    
    # Extract volume mounts with the given prefix
    if [ -f "$env_file" ]; then
        while IFS= read -r line || [ -n "$line" ]; do
            # Skip comments and empty lines
            if [[ "$line" =~ ^[[:space:]]*# ]] || [[ "$line" =~ ^[[:space:]]*$ ]]; then
                continue
            fi
            
            # Check if line has the prefix
            if [[ "$line" == ${prefix}* ]]; then
                # Remove prefix and extract key-value pair
                local kv=${line#${prefix}}
                local container_path=${kv%%=*}
                local host_path=${kv#*=}
                
                # Create host directory if it doesn't exist
                mkdir -p "$host_path"
                
                # Add to volume mounts
                volumes="$volumes -v $container_path:$host_path:Z"
            fi
        done < "$env_file"
    fi
    
    echo "$volumes"
}

# Check if a port is in use
is_port_in_use() {
    local port="$1"
    
    # Check if the port is in use
    if command -v netstat >/dev/null 2>&1; then
        netstat -tuln | grep -q ":$port "
        return $?
    elif command -v ss >/dev/null 2>&1; then
        ss -tuln | grep -q ":$port "
        return $?
    else
        # Fallback to a simple check
        (echo > /dev/tcp/127.0.0.1/$port) >/dev/null 2>&1
        return $?
    fi
}

# Find an available port starting from the given port
find_available_port() {
    local start_port="$1"
    local end_port="${2:-$(($start_port + 100))}"
    local port
    
    for ((port=start_port; port<=end_port; port++)); do
        if ! is_port_in_use "$port"; then
            echo "$port"
            return 0
        fi
    done
    
    echo "Error: No available ports found in range $start_port-$end_port" >&2
    return 1
}
