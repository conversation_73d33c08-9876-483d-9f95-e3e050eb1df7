# ECO Edge Agent MSI Package Overview

## Architecture

The ECO Edge Agent MSI package is built using WiX Toolset v6.0 and follows a single-location installation approach:

### Installation Directory (`%LocalAppData%\EcoEdgeAgent`)
- Main application binaries
- Docker container tar files (`docker_tars` subdirectory)
- Installation scripts (`scripts` subdirectory)
- Flutter application source files (`eco-edge` subdirectory)

### Runtime Directory (`%LocalAppData%\eco-edge\monitor`)
- ECO Edge Monitor Flutter application
- Runtime data and configuration files

## Key Components

### WiX Configuration Files
- `Product.wxs`: Main package definition with directory structure, components, and features
- `FlutterAssets.wxs`: Generated file for Flutter assets
- `FileBrowseDlg.wxs`: Custom UI for configuration file selection

### Custom Actions
- `FileBrowse.dll`: Custom action for JSON file browsing
- PowerShell script execution actions for installation and configuration

### Installation Scripts
- `install.ps1`: Main installation script that handles:
  * Podman installation and initialization
  * Container image loading
  * ECO Edge Monitor installation
  * Configuration file processing
  * Startup shortcut creation

## Build Process

### Validation Phase
- Checks for required directories (`eco-edge`, `docker_tars`, `scripts`)
- Verifies file existence and counts
- Validates critical components before proceeding

### WiX Build Phase
- Generates `FlutterAssets.wxs` using `heat.exe`
- Compiles WiX source files to object files
- Links object files to create the MSI package
- Handles version number updates

## Installation Flow

### User Experience
- Welcome screen
- Installation directory selection
- Optional configuration file selection via custom file browser
- Installation progress
- Completion screen

### Background Process
- Files are copied to appropriate locations
- Podman is installed if not present
- Container images are loaded
- ECO Edge Monitor is installed to the runtime directory
- Windows startup shortcut is created
- Application is started and verified

## Key Features

- **User-friendly Installation**: Installs to user's local application data folder without requiring admin rights
- **Automatic Podman Setup**: Handles Podman installation and initialization
- **Configuration File Selection**: Custom UI for selecting JSON configuration files
- **Process Management**: Handles existing processes before installation
- **Comprehensive Error Handling**: Detailed logging and recovery mechanisms
- **Automatic Startup**: Configures the application to start with Windows
