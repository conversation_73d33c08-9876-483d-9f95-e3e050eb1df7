param(
    [string]$Version = "*******"
)

# PowerShell script to build Eco Edge Agent MSI using WiX Toolset

# Stop on error
$ErrorActionPreference = "Stop"

# Paths (adjust if your structure changes)
$wixDir = Join-Path $PSScriptRoot "WiX"
$wxsFile = Join-Path $wixDir "EcoEdgeAgent.wxs"
$wixObj = Join-Path $wixDir "EcoEdgeAgent.wixobj"
$outputMsi = Join-Path $PSScriptRoot "EcoEdgeAgent-$Version.msi"

# Flutter application source directory
$ecoEdgeSourceDir = Join-Path $PSScriptRoot "eco-edge"

# Verify required directories exist
Write-Host "Verifying required directories..."

# Check eco-edge directory
if (-not (Test-Path $ecoEdgeSourceDir)) {
    Write-Error "Flutter application source directory not found: $ecoEdgeSourceDir"
    Write-Error "Please ensure the eco-edge directory exists with the Flutter application files."
    exit 1
}
Write-Host "Flutter application source directory found: $ecoEdgeSourceDir"

# Verify eco-edge files that will be packaged
$ecoEdgeFiles = Get-ChildItem -Path $ecoEdgeSourceDir -Recurse -File | Measure-Object | Select-Object -ExpandProperty Count
Write-Host "Found $ecoEdgeFiles files in eco-edge directory"

if ($ecoEdgeFiles -eq 0) {
    Write-Error "No files found in the eco-edge directory: $ecoEdgeSourceDir"
    exit 1
}


# Check if docker_tars directory exists and has container images
$dockerTarsDir = Join-Path $PSScriptRoot "docker_tars"
if (-not (Test-Path $dockerTarsDir)) {
    Write-Error "Docker tars directory not found: $dockerTarsDir"
    Write-Error "The docker_tars directory must exist and contain the required container image files."
    exit 1
}

# Check for required container image files
$octopusServiceTar = Join-Path $dockerTarsDir "octopus-service.tar"
$ewpTar = Join-Path $dockerTarsDir "ewp.tar"

if (-not (Test-Path $octopusServiceTar)) {
    Write-Error "Required container image file not found: $octopusServiceTar"
    exit 1
}

if (-not (Test-Path $ewpTar)) {
    Write-Error "Required container image file not found: $ewpTar"
    exit 1
}

Write-Host "Found required container image files in docker_tars directory."

# Check for scripts folder and install.ps1 file
$scriptsDir = Join-Path $PSScriptRoot "scripts"
if (-not (Test-Path $scriptsDir)) {
    Write-Error "Scripts directory not found: $scriptsDir"
    Write-Error "The scripts directory must exist and contain the required installation scripts."
    exit 1
}

$installScript = Join-Path $scriptsDir "install.ps1"
if (-not (Test-Path $installScript)) {
    Write-Error "Required installation script not found: $installScript"
    Write-Error "The install.ps1 script is required to install the application."
    exit 1
}

Write-Host "Found required installation scripts in scripts directory."

# Ensure WiX tools are available - look in common installation locations
function Find-WiXTools {
    # Using WiX 5.x with wix.exe command
    $global:WixCmd = $null
    
    # Check if wix.exe is in PATH (for WiX 5.x or 6.0)
    if (Get-Command wix.exe -ErrorAction SilentlyContinue) {
        $global:WixCmd = "wix.exe"
        Write-Host "Found WiX CLI (wix.exe) in PATH"
        return $true
    }

    # WiX 5.x paths
    $wixPaths = @(
        # WiX v5.x paths
        "C:\Program Files\WiX Toolset v5.0\bin",
        "C:\Program Files\WiX Toolset v5.0\tools",
        "C:\Program Files (x86)\WiX Toolset v5.0\bin",
        "C:\Program Files (x86)\WiX Toolset v5.0\tools"
    )
    
    foreach ($path in $wixPaths) {
        if (Test-Path (Join-Path $path "wix.exe")) {
            Write-Host "Found WiX v5.0 CLI in: $path"
            # Add to PATH for this session
            $env:PATH = "$path;$env:PATH"
            $global:WixCmd = "wix.exe"

            return $true
        }
    }
    
    # No fallback to older WiX versions - only using WiX 5.x
    Write-Host "No WiX 5.x installation found."
    return $false
}

# Check if WiX tools are available

if (-not (Find-WiXTools)) {
    Write-Error "WiX Toolset not found. WiX v5.0 should be installed in one of these locations:"
    Write-Error "- C:\Program Files\WiX Toolset v5.0\bin"
    Write-Error "- C:\Program Files\WiX Toolset v5.0\tools"
    Write-Error "- C:\Program Files (x86)\WiX Toolset v5.0\bin"
    Write-Error "- C:\Program Files (x86)\WiX Toolset v5.0\tools"
    Write-Error "Please ensure WiX v5.0 is installed and the directory containing wix.exe is in your PATH."
    exit 1
}

# Update version in the WiX file
Write-Host "Setting MSI version to $Version ..."

# Update version in EcoEdgeAgent.wxs
$wxsContent = Get-Content $wxsFile
$wxsContent = $wxsContent -replace '<Package([^>]*)Version="[^"]+"', ('<Package$1Version="' + $Version + '"')
Set-Content $wxsFile $wxsContent

# Build the MSI using WiX v5.x CLI
Push-Location $wixDir



# WiX v5.x uses a single wix.exe command
Write-Host "Building MSI with WiX v5.x CLI..."

# Using WiX 5.0 tools with proper extension format
Write-Host "Building with WiX 5.0 extensions..." -ForegroundColor Green

# Find the WiX UI and Util extensions - searching in common locations
function Find-WixExtension {
    param(
        [string]$ExtensionName
    )
    
    $potentialPaths = @(
        # NuGet package path
        "$env:USERPROFILE\.nuget\packages\wix.$($ExtensionName).wixext\5.0.0\tools\WixToolset.$($ExtensionName).wixext.dll",
        # Direct WiX installation path
        "C:\Program Files\WiX Toolset\5.0\$ExtensionName\WixToolset.$($ExtensionName).wixext.dll",
        # Local user packages
        "$env:LOCALAPPDATA\WiX\packages\wix.$($ExtensionName).wixext\5.0.0\tools\WixToolset.$($ExtensionName).wixext.dll"
    )
    
    foreach ($path in $potentialPaths) {
        if (Test-Path $path) {
            Write-Host "Found $ExtensionName extension at: $path" -ForegroundColor Green
            return $path
        }
    }
    
    # If not found in known locations, try using the extension name only and let WiX resolve it
    Write-Host "$ExtensionName extension not found in known locations. Using name reference only." -ForegroundColor Yellow
    return "WixToolset.$($ExtensionName).wixext"
}

$wixUIExtPath = Find-WixExtension -ExtensionName "UI"
$wixUtilExtPath = Find-WixExtension -ExtensionName "Util"

# We'll attempt to build with WiX 5.0 tools using full paths to extensions
& wix.exe build -v -o $outputMsi $wxsFile -ext $wixUIExtPath -ext $wixUtilExtPath

$buildResult = $LASTEXITCODE
if ($buildResult -ne 0) { 
    Pop-Location
    throw "WiX build failed with exit code $buildResult" 
}
Write-Host "WiX build completed successfully" -ForegroundColor Green

Pop-Location

Write-Host "MSI build complete: $outputMsi"
