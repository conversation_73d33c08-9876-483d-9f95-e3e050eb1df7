Package: eco-edge
Version: 1.1.1
Section: utils
Priority: optional
Architecture: amd64
Depends: docker.io
Recommends: docker-compose
Maintainer: Eco Edge Team <<EMAIL>>
Description: Eco Edge application for efficient edge computing and monitoring
 Eco Edge is a containerized application that provides:
 - A backend REST API service (Node.js/Express)
 - A frontend web interface (Nginx with proxy configuration)
 - Monitoring and management capabilities
 .
 This package installs and configures the Eco Edge application
 using Docker containers.
